# DefaultValueSetter 组件说明

## 概述

DefaultValueSetter 是一个用于设置不同类型默认值的通用组件，支持多种数据类型和约束条件。该组件能够根据约束类型自动渲染相应的输入组件，并处理不同类型的默认值。

## 主要功能

### 1. 支持的数据类型

- **字符串类型 (STRING)**: 文本输入，支持长度限制和正则验证
- **整数类型 (INT)**: 数字输入，支持最小值、最大值和步长设置
- **浮点数类型 (FLOAT)**: 浮点数输入，支持精度设置
- **布尔类型 (BOOLEAN)**: 开关组件
- **日期时间类型 (DATE_TIME)**: 日期选择器，支持不同日期格式
- **富文本类型 (RICH_TEXT)**: 多行文本输入
- **对象类型 (OBJECT)**: 下拉选择，支持动态数据源
- **上传类型 (ATTACHMENT)**: 文件上传组件

### 2. localDefaultValue 的类型处理

`localDefaultValue` 是一个计算属性，用于双向绑定默认值，它会根据不同的约束类型进行相应的类型转换：

#### 类型转换逻辑

```typescript
const formatDefaultValue = (value) => {
  if (value === undefined || value === null) return ''

  // 根据约束类型格式化值
  switch (props.constraintType) {
    case constraintTypeMap.INT:
    case constraintTypeMap.FLOAT:
      return Number(value)  // 转换为数字类型
    case constraintTypeMap.BOOLEAN:
      return Boolean(value) // 转换为布尔类型
    case constraintTypeMap.OBJECT:
      return value          // 对象类型保持原样
    default:
      return String(value)  // 默认转换为字符串
  }
}
```

#### 不同类型的默认值示例

1. **字符串类型**
   ```javascript
   localDefaultValue.value = "示例文本"  // string
   ```

2. **整数类型**
   ```javascript
   localDefaultValue.value = 42  // number (整数)
   ```

3. **浮点数类型**
   ```javascript
   localDefaultValue.value = 3.14  // number (浮点数)
   ```

4. **布尔类型**
   ```javascript
   localDefaultValue.value = true  // boolean
   ```

5. **日期类型**
   ```javascript
   localDefaultValue.value = "2024-01-01"  // string (日期字符串)
   ```

6. **对象类型**
   ```javascript
   localDefaultValue.value = "option_value"  // string (选中的选项值)
   ```

7. **上传类型**
   ```javascript
   localDefaultValue.value = [
     { url: "file1.jpg", name: "文件1" },
     { url: "file2.jpg", name: "文件2" }
   ]  // array (文件列表)
   ```

### 3. 约束验证

组件提供了完整的约束验证功能：

#### 数字类型验证
- 最小值/最大值检查
- 整数类型的整数验证
- 浮点数精度控制

#### 字符串类型验证
- 最小/最大长度检查
- 正则表达式模式验证

#### 上传类型验证
- 文件数量限制
- 文件大小限制
- 文件类型限制

### 4. 组件属性

```typescript
interface Props {
  // 约束类型
  constraintType: string
  // 约束列表数据
  constraintList: Object
  // 当前默认值
  defaultValue: string | number | boolean | Array | Object
  // 当前默认别名
  defaultName: string
  // 选项数据（用于下拉选择类型）
  options: Object
  // 是否禁用编辑
  disabled: boolean
  // 是否正在加载
  loading: boolean
}
```

### 5. 事件

```typescript
// 更新默认值
emit('update:defaultValue', value)
// 更新默认别名
emit('update:defaultName', name)
// 更新选项配置
emit('update:options', options)
```

## 使用示例

### 基本用法

```vue
<template>
  <DefaultValueSetter
    :constraintType="constraintTypeMap.STRING"
    :constraintList="stringConstraints"
    v-model:defaultValue="defaultValue"
    v-model:defaultName="defaultName"
  />
</template>

<script setup>
import { ref } from 'vue'
import { constraintTypeMap, keyMap } from '@/views/system-configuration/Config/help'
import DefaultValueSetter from './DefaultValueSetter.vue'

const defaultValue = ref('')
const defaultName = ref('')
const stringConstraints = ref({
  [keyMap.minLength]: { value: 2 },
  [keyMap.maxLength]: { value: 50 },
  [keyMap.placeholder]: { value: '请输入文本' }
})
</script>
```

### 数字类型示例

```vue
<DefaultValueSetter
  :constraintType="constraintTypeMap.INT"
  :constraintList="{
    [keyMap.min]: { value: 1 },
    [keyMap.max]: { value: 100 },
    [keyMap.step]: { value: 1 }
  }"
  v-model:defaultValue="numberValue"
  v-model:defaultName="numberName"
/>
```

### 对象类型示例

```vue
<DefaultValueSetter
  :constraintType="constraintTypeMap.OBJECT"
  :constraintList="objectConstraints"
  v-model:defaultValue="objectValue"
  v-model:defaultName="objectName"
  v-model:options="objectOptions"
/>
```

## 注意事项

1. **类型安全**: 组件会自动进行类型转换，确保 `localDefaultValue` 的类型与约束类型匹配
2. **验证机制**: 在值变更时会自动进行约束验证，不符合条件的值会被拒绝
3. **动态渲染**: 根据约束类型动态渲染相应的输入组件
4. **数据源选择**: 对象类型支持动态选择数据源（枚举、材料分类、产品分类等）

## 扩展性

组件设计具有良好的扩展性，可以通过以下方式进行扩展：

1. 在 `constraintTypeMap` 中添加新的约束类型
2. 在 `componentRap` 中添加对应的组件映射
3. 在 `keyMap` 中添加新的约束键
4. 在组件中添加相应的类型处理逻辑

这样的设计使得组件能够灵活适应不同的业务需求，同时保持代码的可维护性。

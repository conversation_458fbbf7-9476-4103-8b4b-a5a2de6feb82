<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { VxeTableInstance } from 'vxe-table'
import {
  LastDataStatusEnum,
  ProductCategoryAPI,
  updateProductCategoryStatus,
  getProductCategoryList
} from '@/views/basic-library-manage/product-library/api/product-category'
import { statusConst } from '@/views/basic-library-manage/const'
import { AddCategoryTypeEnum } from '@/views/basic-library-manage/product-library/const'
import AddCategoryDialog from '@/views/basic-library-manage/product-library/components/AddCategoryDialog.vue'
import { Icon } from '@/components/Icon'
import { TypeOrPropertyType } from '@/views/system-configuration/Config/help'
import { LayoutForm } from '@/components/LayoutForm'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { hasPermission } from '@/directives/permission/hasPermi'
import { useTableHeight } from '@/hooks/web/useTableHeight'
const router = useRouter()
defineOptions({
  name: 'ProductCategory'
})

const useConst = () => {
  const statusMap = statusConst.statusMap

  return {
    statusMap
  }
}

const { statusMap } = useConst()
const layoutFormRef = ref()
const useQuery = () => {
  const tableRef = ref<VxeTableInstance>()
  const formData = ref<ProductCategoryAPI.Request>({
    categoryName: ''
  })
  const tableData = ref<ProductCategoryAPI.Data[]>([])
  const queryLoading = ref(false)

  function addIndex(array: ProductCategoryAPI.Data[], parentIndex = 1) {
    array.forEach((item) => {
      item.index = parentIndex
      if (item.childList && item.childList.length > 0) {
        addIndex(item.childList, parentIndex + 1)
      }
    })
  }

  const handleQuery = async () => {
    queryLoading.value = true
    const [error, result] = await getProductCategoryList(formData.value)
    queryLoading.value = false
    if (error === null && result?.datas) {
      tableData.value = result.datas || []
      addIndex(tableData.value)
    }
  }

  onActivated(handleQuery)

  return {
    tableRef,
    formData,
    tableData,
    queryLoading,
    handleQuery
  }
}

const { tableRef, formData, tableData, queryLoading, handleQuery } = useQuery()

Promise.all([handleQuery()])

const useOperation = () => {
  const currentRow = ref<ProductCategoryAPI.Data | null>(null)
  const addDialogType = ref(AddCategoryTypeEnum.ADD)
  const addDialogVisible = ref(false)
  const handleOpenAddDialog = (row: ProductCategoryAPI.Data | null, type: AddCategoryTypeEnum) => {
    currentRow.value = row
    addDialogType.value = type
    addDialogVisible.value = true
  }

  const handleStatusChange = async (status: string, row: ProductCategoryAPI.Data) => {
    ElMessageBox.alert('当前分类及对应的子层级分类都会同时更新状态', '提示', {
      showCancelButton: true,
      showClose: true,
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = instance.cancelButtonLoading = true
          const [error, result] = await updateProductCategoryStatus({
            id: row.id,
            status,
            beforeStatus: row.status
          })
          instance.confirmButtonLoading = instance.cancelButtonLoading = false
          if (error === null && result) {
            ElMessage.success(result.msg || '操作成功')
            done()
            await handleQuery()
          }
        } else {
          done()
        }
      }
    }).catch(() => {})
  }

  return {
    currentRow,
    addDialogType,
    addDialogVisible,
    handleStatusChange,
    handleOpenAddDialog
  }
}

const { currentRow, addDialogType, addDialogVisible, handleStatusChange, handleOpenAddDialog } =
  useOperation()

const handleOpenProperty = (row: ProductCategoryAPI.Data) => {
  router.push({
    name: 'propertyConstrait',
    query: {
      id: row.id,
      type: TypeOrPropertyType.type
    }
  })
}
const { handleExport: exportFn, loading: exportLoading } = useOmsExport(false)
const downloadFunc = () => {
  const reqParam = JSON.stringify(formData.value)
  exportFn({
    exportType: 'product-category-export',
    reqParam
  })
}
// 重置表单
const handleReset = () => {
  if (!layoutFormRef.value) return
  console.log(layoutFormRef.value)
  layoutFormRef.value.formRef.resetFields()
  handleQuery()
}
const maxHeight = useTableHeight({ tableRef: tableRef })
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_200px)] overflow-x-hidden overflow-y-auto">
        <LayoutForm
          ref="layoutFormRef"
          @reset="handleReset"
          @search="handleQuery"
          :model="formData"
          query-form
          :span="6"
        >
          <ElFormItem label="分类名称" prop="categoryName">
            <ElInput
              v-model="formData.categoryName"
              clearable
              placeholder="请输入分类名称"
              @change="handleQuery"
            />
          </ElFormItem>
        </LayoutForm>
        <div class="flex">
          <ElButton
            type="primary"
            class="mb-2"
            v-hasPermi="['productCategory:addList']"
            @click="handleOpenAddDialog(null, AddCategoryTypeEnum.ADD)"
            ><Icon icon="ep:plus" /> 新增</ElButton
          >
          <ElButton
            type="primary"
            v-hasPermi="['productCategory:download']"
            @click="downloadFunc"
            :loading="exportLoading"
          >
            <Icon class="mr-0.5" icon="ep:upload-filled" />
            导出</ElButton
          >
        </div>
        <VxeTable
          ref="tableRef"
          :data="tableData"
          :loading="queryLoading"
          :max-height="maxHeight - 100"
          :min-height="200"
          :row-config="{ keyField: 'id' }"
          :tree-config="{ childrenField: 'childList', reserve: true }"
        >
          <VxeColumn field="categoryCnName" title="分类名称" tree-node />
          <VxeColumn field="levelCode" title="层级编码" />
          <VxeColumn field="categoryEnName" title="分类英文名称" />
          <VxeColumn field="levelCode" title="分类英文名称" />
          <VxeColumn field="structureItemName" title="款式结构" />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: statusMap } }"
            field="status"
            title="状态"
          />
          <VxeColumn field="wmsCategoryName" title="商品分类" />
          <VxeColumn field="wmsCategoryCode" title="商品分类编码" />
          <VxeColumn :show-overflow="false" title="操作">
            <template #default="{ row }: { row: ProductCategoryAPI.Data }">
              <div class="flex justify-around items-center">
                <ElSwitch
                  v-if="
                    row.status?.toLowerCase() === LastDataStatusEnum.Start.toLowerCase() ||
                    row.status?.toLowerCase() === LastDataStatusEnum.Ban.toLowerCase()
                  "
                  :model-value="row.status"
                  active-value="start"
                  inactive-value="ban"
                  @change="(val:string) => handleStatusChange(val, row)"
                />
                <el-dropdown>
                  <el-button type="text"> 操作 </el-button>
                  <template #dropdown>
                    <el-dropdown-item
                      v-if="
                        row.status?.toLowerCase() === LastDataStatusEnum.Draft.toLowerCase() &&
                        hasPermission(['productCategory:update'])
                      "
                    >
                      <ElButton link @click="handleOpenAddDialog(row, AddCategoryTypeEnum.UPDATE)">
                        修改分类
                      </ElButton></el-dropdown-item
                    >

                    <el-dropdown-item v-if="hasPermission(['productCategory:add'])">
                      <ElButton link @click="handleOpenAddDialog(row, AddCategoryTypeEnum.ADD)">
                        新增子分类
                      </ElButton></el-dropdown-item
                    >
                    <el-dropdown-item v-if="hasPermission(['productCategory:detail'])">
                      <ElButton @click="handleOpenProperty(row)" link> 分类详情 </ElButton>
                    </el-dropdown-item>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </VxeColumn>
        </VxeTable>
      </div>
      <AddCategoryDialog
        v-model="addDialogVisible"
        :current-row="currentRow"
        :type="addDialogType"
        @refresh="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>

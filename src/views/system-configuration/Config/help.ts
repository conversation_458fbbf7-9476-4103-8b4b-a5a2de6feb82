import { SelectPlus } from '@/components/Business/SelectPlus'
import {
  ElDatePicker,
  ElInput,
  ElInputNumber,
  ElUpload,
  ElSwitch,
  ElSelect,
  ElCheckbox,
  ElCascader,
  ElCheckboxGroup
} from 'element-plus'
import { ApiSelect } from '@/components/ApiSelect'
import { typeTree } from '@/api/systemConfiguration/type'
export enum StartOrBan {
  start = 'start',
  ban = 'ban'
}
/**枚举相关配置*/
export const EnumConfig = {
  // 搜索
  EnumFormConfig: [
    {
      label: '枚举内部值',
      field: 'enumeratedValue',
      component: ElInput,
      props: {
        placeholder: '请输入内部值'
      }
    },
    {
      label: '枚举名称',
      field: 'enumeratedDesc',
      component: ElInput,
      props: {
        placeholder: '请输入枚举名称'
      }
    }
  ],
  // table
  EnumTableConfig: [
    {
      type: 'checkbox',
      fixed: 'left',
      width: 40
    },
    { type: 'seq', fixed: 'left', width: 60, title: '序号' },
    { field: 'enumeratedValue', sortable: true, title: '枚举内部值', minWidth: 120 },
    { field: 'enumeratedClassificationDesc', title: '分类', minWidth: 120 },
    { field: 'enumeratedCode', title: '编码', minWidth: 120 },
    { field: 'enumeratedDesc', title: '枚举名称', sortable: true, minWidth: 120 },
    { field: 'createByIdName', title: '创建者', minWidth: 150 },
    { field: 'createTime', title: '创建时间', sortable: true, minWidth: 150 },
    { field: 'modifyByIdName', title: '修改者', minWidth: 150 },
    { field: 'modifyTime', title: '修改时间', minWidth: 150, sortable: true },
    { field: 'status', title: '状态', minWidth: 150 },
    {
      title: '操作',
      width: 80,
      fixed: 'right',
      slots: {
        default: 'operation'
      }
    }
  ],
  // 枚举编辑
  EnumEditConfig: [
    {
      label: '枚举值',
      field: 'enumeratedValue',
      component: ElInput,
      span: 6,
      props: {
        maxlength: 32,
        placeholder: '请输入内部值'
      }
    },
    {
      label: '枚举名称',
      field: 'enumeratedDesc',
      component: ElInput,
      span: 6,
      props: {
        maxlength: 64,
        placeholder: '请输入枚举名称'
      }
    },
    {
      label: '枚举编码',
      field: 'enumeratedCode',
      component: ElInput,
      span: 6,
      props: {
        maxlength: 32,
        placeholder: '请输入枚举编码'
      }
    },
    {
      label: '分类',
      component: SelectPlus,
      needDisable: true,
      field: 'enumeratedClassificationId',
      span: 8,
      props: {
        cascadeClear: true,
        apiKey: 'getEnumeratedClassificationList',
        filterable: true,
        virtualized: true,
        configuration: {
          key: 'id',
          value: 'id',
          label: 'enumeratedClassificationDesc'
        }
      }
    },
    {
      label: '状态',
      field: 'statusCode',
      component: SelectPlus,
      span: 6,
      props: {
        radio: true,
        configuration: {
          value: 'value',
          label: 'label'
        },
        selectOptions: [
          {
            label: '启用',
            value: StartOrBan.start
          },
          {
            label: '禁用',
            value: StartOrBan.ban
          }
        ]
      }
    }
  ],
  // 枚举类型编辑新增
  EnumTypeConfig: [
    {
      label: '分类名称',
      field: 'enumeratedClassificationDesc',
      component: ElInput,
      span: 6,
      rules: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/u,
          message: '不能输入特殊字符',
          trigger: 'blur'
        }
      ],
      props: {
        maxlength: 32,
        placeholder: '请输入分类名称'
      }
    }
  ],
  // 属性选枚举
  EnumSelectConfig: [
    { type: 'radio', fixed: 'left', width: 40 },
    { field: 'code', title: '编码', width: 200 },
    {
      title: '枚举名称',
      field: 'name'
    }
  ],
  // 材料分类
  MaterialConfig: [
    {
      fixed: 'left',
      width: 40,
      type: 'checkbox'
    },
    { field: 'categoryCode', treeNode: true, title: '材料编码', width: 200 },
    {
      title: '材料名称',
      field: 'categoryCnName'
    }
  ],
  // 产品分类
  ProductConfig: [
    {
      type: 'checkbox',
      fixed: 'left',
      width: 40
    },
    { field: 'categoryCode', treeNode: true, title: '产品分类编码', width: 200 },
    {
      title: '产品分类名称',
      field: 'categoryCnName'
    }
  ]
}
/***
 * 属性相关配置
 */
export const PropertyConfig = {
  PropertyFormConfig: [
    {
      label: '属性内部值',
      field: 'value',
      component: ElInput,
      span: 6,
      props: {
        placeholder: '请输入属性内部值'
      }
    },
    {
      label: '属性名称',
      field: 'name',
      component: ElInput,
      span: 6,
      props: {
        placeholder: '请输入属性名称'
      }
    }
  ],
  PropertyTableConfig: [
    { type: 'checkbox', fixed: 'left', width: 40 },
    { type: 'seq', width: 60, title: '序号' },
    { field: 'value', title: '属性内部值', sortable: true, minWidth: 120 },
    { field: 'categoryName', title: '分类', minWidth: 120 },
    { field: 'typeItemName', title: '数据类型', sortable: true, minWidth: 100 },
    { field: 'name', title: '属性名称', sortable: true, minWidth: 120 },
    { field: 'description', title: '属性描述', minWidth: 150 },
    { field: 'createByName', title: '创建者', minWidth: 150 },
    { field: 'createTime', title: '创建时间', sortable: true, minWidth: 150 },
    { field: 'modifyByName', title: '修改者', minWidth: 150 },
    { field: 'modifyTime', title: '修改时间', sortable: true, minWidth: 150 },
    { field: 'statusItemName', title: '状态', minWidth: 150 },
    {
      title: '操作',
      width: 80,
      fixed: 'right',
      slots: {
        default: 'operation'
      }
    }
  ],
  PropertyEditConfig: [
    {
      label: '属性内部值',
      field: 'value',
      component: ElInput,
      span: 6,
      needDisable: true,
      props: {
        maxlength: 32,
        placeholder: '请输入内部值'
      }
    },
    {
      label: '数据类型',
      component: SelectPlus,
      needDisable: true,
      field: 'type',
      span: 8,
      props: {
        cascadeClear: true,
        apiKey: 'PROPERTY_DATA_TYPE',
        filterable: true,
        virtualized: true
      }
    },
    {
      label: '属性名称',
      field: 'name',
      component: ElInput,
      span: 6,
      props: {
        maxlength: 64,
        placeholder: '请输入属性名称'
      }
    },
    {
      label: '分类',
      component: SelectPlus,
      field: 'categoryId',
      needDisable: true,
      span: 8,
      props: {
        cascadeClear: true,
        apiKey: 'getCategoryListApi',
        filterable: true,
        virtualized: true,
        configuration: {
          key: 'selectorValue',
          value: 'selectorValue',
          label: 'selectorKey'
        }
      }
    },
    {
      label: '属性描述',
      field: 'description',
      component: ElInput,
      span: 6,
      props: {
        type: 'textarea',
        maxlength: 500,
        rows: 3,
        placeholder: '请输入属性描述'
      }
    },
    {
      label: '状态',
      field: 'status',
      component: SelectPlus,
      span: 6,
      props: {
        radio: true,
        configuration: {
          value: 'value',
          label: 'label'
        },
        selectOptions: [
          {
            label: '启用',
            value: StartOrBan.start,
            key: StartOrBan.start
          },
          {
            label: '禁用',
            value: StartOrBan.ban
          }
        ]
      }
    }
  ],
  PropertyTypeConfig: [
    {
      label: '分类名称',
      field: 'categoryName',
      component: ElInput,
      span: 6,
      rules: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/u,
          message: '不能出现特殊符号',
          trigger: 'blur'
        }
      ],
      props: {
        maxlength: 32,
        placeholder: '请输入分类名称'
      }
    }
  ]
}

/**
 * 类型配置
 * */
export const TypeConfig = {
  TypeEditConfig: [
    {
      label: '显示名称',
      field: 'typeName',
      component: ElInput,
      span: 6,
      props: {
        maxlength: 32,
        placeholder: '请输入显示名称'
      }
    },
    {
      label: '内部名称',
      needDisable: true,
      field: 'code',
      component: ElInput,
      span: 6,
      props: {
        maxlength: 32,
        placeholder: '请输入内部名称'
      }
    },
    {
      label: '说明',
      field: 'description',
      component: ElInput,
      span: 6,
      props: {
        type: 'textarea',
        maxlength: 500,
        rows: 3,
        placeholder: '请输入说明'
      }
    }
  ],
  TypeTableConfig: [
    { type: 'radio', fixed: 'left', width: 40 },
    {
      title: '属性名称',
      field: 'name',
      minWidth: 80
    },
    {
      title: '属性内部值',
      field: 'value',
      minWidth: 80
    },
    {
      title: '数据类型',
      field: 'typeItemName',
      minWidth: 80
    },
    {
      title: '是否继承',
      field: 'isExtends',
      slots: {
        default: 'isExtends'
      }
    },
    {
      title: '操作',
      width: 80,
      fixed: 'right',
      slots: {
        default: 'operation'
      }
    }
  ],
  typeDialogFormConfig: [
    {
      label: '父类型',
      field: 'categoryId',
      component: ApiSelect,
      span: 6,
      rules: [{ required: true, message: '请选择类型', trigger: 'change' }],
      props: {
        'api-config': {
          api: typeTree,
          config: {
            label: 'typeShowName',
            value: 'typeId',
            children: 'typeChild'
          }
        },
        required: true,
        clearable: true,
        filterable: true,
        virtualized: true
      }
    },
    {
      label: '内部名称',
      field: 'name',
      needDisable: true,
      component: ElInput,
      span: 6,
      rules: [
        { required: true, message: '请输入内部名称', trigger: 'blur' },
        {
          pattern: /^[A-Za-z0-9_]{1,32}$/,
          message: '必须为英文字母or数字or_，特殊符号只可以为_',
          trigger: 'blur'
        }
      ],
      props: { required: true, maxlength: 32, placeholder: '请输入内部名称' }
    },
    {
      label: '显示名称',
      field: 'shoeName',
      component: ElInput,
      span: 6,
      rules: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
      props: {
        required: true,
        maxlength: 32,
        placeholder: '请输入显示名称'
      }
    },
    {
      label: '说明',
      field: 'remark',
      component: ElInput,
      span: 6,
      props: {
        type: 'textarea',
        maxlength: 500,
        rows: 3,
        placeholder: '请输入说明'
      }
    }
  ],
  typeDialogTableConfig: [
    {
      type: 'checkbox',
      fixed: 'left',
      width: 40
    },
    {
      label: '属性内部名称',
      field: 'typeName',
      minWidth: 120
    },
    {
      label: '分类',
      field: 'code',
      minWidth: 120
    },
    {
      label: '数据类型',
      field: 'description',
      minWidth: 120
    },
    {
      label: '属性名称',
      field: 'description',
      minWidth: 120
    },
    {
      label: '属性描述',
      field: 'description',
      minWidth: 120
    }
  ]
}

export enum LeftType {
  enum = 'enum',
  property = 'property',
  type = 'type'
}
export interface selectType {
  label: string
  value: string
  id: string
  [x: string]: string | selectType[] | undefined
  children?: selectType[]
}
export enum TypeOrPropertyType {
  type = 'PROPERTY_CONSTRAINT',
  property = 'TYPE_CONSTRAINT'
}

//属性类型映射
export const constraintTypeMap = {
  STRING: 'INPUT_STRING_TYPE',
  INT: 'INPUT_NUMBER_TYPE',
  BOOLEAN: 'SWITCH_TYPE',
  ATTACHMENT: 'UPLOAD_TYPE',
  OBJECT: 'SELECT_TYPE',
  RICH_TEXT: 'TEXTAREA_TYPE',
  FLOAT: 'INPUT_NUMBER_TYPE',
  DATE_TIME: 'DATE_TYPE'
}
//组件映射
export const componentRap = {
  BOOLEAN: ElSwitch,
  STRING: ElInput,
  OBJECT: ElSelect,
  NUMBER: ElInputNumber,
  DATE: ElDatePicker,
  SELECT_TYPE: ElSelect,
  INPUT_STRING_TYPE: ElInput,
  INPUT_NUMBER_TYPE: ElInputNumber,
  SWITCH_TYPE: ElSwitch,
  UPLOAD_TYPE: ElUpload,
  TEXTAREA_TYPE: ElInput,
  DATE_TYPE: ElDatePicker
}
//组件类型映射
export const componentType = {
  SELECT: ElSelect,
  CASCADER: ElCascader,
  RADIOGROUP: ElRadioGroup,
  CHECKBOXGROUP: ElCheckboxGroup
}
export const sourceEnum = {
  enum: 'ENUM',
  material: 'MATERIAL',
  category: 'CATEGORY'
}
//配置需要枚举下拉的code
export const constArr = {
  dataType: 'ENUMERATED_0111',
  format: 'ENUMERATED_0112',
  caseConfiguration: 'ENUMERATED_0113',
  repeat: 'ENUMERATED_0114',
  component: 'ENUMERATED_0115',
  visibility: 'ENUMERATED_0116',
  source: 'ENUMERATED_SOURCE',
  //显示规则对应枚举
  name: 'ENUM0063'
}
export const entityName = {
  [LeftType.enum]: '枚举类型',
  [LeftType.property]: '属性分类',
  [LeftType.type]: '类型'
}
export const keyMap = {
  link: 'link',
  readonly: 'readonly',
  minLength: 'minLength',
  maxLength: 'maxLength',
  rows: 'rows',
  clearable: 'clearable',
  disabled: 'disabled',
  required: 'required',
  filterable: 'filterable',
  placeholder: 'placeholder',
  multiple: 'multiple',
  precision: 'precision',
  editable: 'editable',
  limit: 'limit',
  accept: 'accept',
  sizeLimit: 'sizeLimit',
  format: 'format',
  // 数字类型约束
  min: 'min',
  max: 'max',
  step: 'step',
  // 日期类型约束
  dateType: 'dateType',
  // 上传类型约束
  maxSize: 'maxSize',
  // 字符串验证约束
  pattern: 'pattern',
  dataType: 'dataType',
  isRange: 'isRange',
  component: 'component'
}

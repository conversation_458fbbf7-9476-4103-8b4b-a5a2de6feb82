<template>
  <div class="default-value-setter-example">
    <h2>DefaultValueSetter 组件使用示例</h2>
    
    <div class="example-section">
      <h3>1. 字符串类型 (STRING)</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.STRING"
        :constraintList="stringConstraints"
        v-model:defaultValue="stringValue"
        v-model:defaultName="stringName"
      />
      <p>当前值: {{ stringValue }} (类型: {{ typeof stringValue }})</p>
      <p>默认别名: {{ stringName }}</p>
    </div>

    <div class="example-section">
      <h3>2. 整数类型 (INT)</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.INT"
        :constraintList="intConstraints"
        v-model:defaultValue="intValue"
        v-model:defaultName="intName"
      />
      <p>当前值: {{ intValue }} (类型: {{ typeof intValue }})</p>
      <p>默认别名: {{ intName }}</p>
    </div>

    <div class="example-section">
      <h3>3. 浮点数类型 (FLOAT)</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.FLOAT"
        :constraintList="floatConstraints"
        v-model:defaultValue="floatValue"
        v-model:defaultName="floatName"
      />
      <p>当前值: {{ floatValue }} (类型: {{ typeof floatValue }})</p>
      <p>默认别名: {{ floatName }}</p>
    </div>

    <div class="example-section">
      <h3>4. 布尔类型 (BOOLEAN)</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.BOOLEAN"
        :constraintList="booleanConstraints"
        v-model:defaultValue="booleanValue"
        v-model:defaultName="booleanName"
      />
      <p>当前值: {{ booleanValue }} (类型: {{ typeof booleanValue }})</p>
      <p>默认别名: {{ booleanName }}</p>
    </div>

    <div class="example-section">
      <h3>5. 日期时间类型 (DATE_TIME)</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.DATE_TIME"
        :constraintList="dateConstraints"
        v-model:defaultValue="dateValue"
        v-model:defaultName="dateName"
      />
      <p>当前值: {{ dateValue }} (类型: {{ typeof dateValue }})</p>
      <p>默认别名: {{ dateName }}</p>
    </div>

    <div class="example-section">
      <h3>6. 富文本类型 (RICH_TEXT)</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.RICH_TEXT"
        :constraintList="richTextConstraints"
        v-model:defaultValue="richTextValue"
        v-model:defaultName="richTextName"
      />
      <p>当前值: {{ richTextValue }} (类型: {{ typeof richTextValue }})</p>
      <p>默认别名: {{ richTextName }}</p>
    </div>

    <div class="example-section">
      <h3>7. 对象类型 (OBJECT)</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.OBJECT"
        :constraintList="objectConstraints"
        v-model:defaultValue="objectValue"
        v-model:defaultName="objectName"
        v-model:options="objectOptions"
      />
      <p>当前值: {{ objectValue }} (类型: {{ typeof objectValue }})</p>
      <p>默认别名: {{ objectName }}</p>
      <p>选项配置: {{ objectOptions }}</p>
    </div>

    <div class="example-section">
      <h3>8. 上传类型 (ATTACHMENT)</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.ATTACHMENT"
        :constraintList="attachmentConstraints"
        v-model:defaultValue="attachmentValue"
        v-model:defaultName="attachmentName"
      />
      <p>当前值: {{ attachmentValue }} (类型: {{ typeof attachmentValue }})</p>
      <p>默认别名: {{ attachmentName }}</p>
    </div>

    <div class="debug-section">
      <h3>调试信息</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { constraintTypeMap, keyMap } from '@/views/system-configuration/Config/help'
import DefaultValueSetter from './DefaultValueSetter.vue'

// 字符串类型示例
const stringValue = ref('')
const stringName = ref('')
const stringConstraints = ref({
  [keyMap.minLength]: { value: 2 },
  [keyMap.maxLength]: { value: 50 },
  [keyMap.placeholder]: { value: '请输入字符串' },
  [keyMap.pattern]: { value: '^[a-zA-Z0-9\\u4e00-\\u9fa5]+$' }
})

// 整数类型示例
const intValue = ref(0)
const intName = ref('')
const intConstraints = ref({
  [keyMap.min]: { value: 1 },
  [keyMap.max]: { value: 100 },
  [keyMap.step]: { value: 1 },
  [keyMap.placeholder]: { value: '请输入整数' }
})

// 浮点数类型示例
const floatValue = ref(0.0)
const floatName = ref('')
const floatConstraints = ref({
  [keyMap.min]: { value: 0.1 },
  [keyMap.max]: { value: 99.9 },
  [keyMap.step]: { value: 0.1 },
  [keyMap.precision]: { value: 2 },
  [keyMap.placeholder]: { value: '请输入浮点数' }
})

// 布尔类型示例
const booleanValue = ref(false)
const booleanName = ref('')
const booleanConstraints = ref({})

// 日期时间类型示例
const dateValue = ref('')
const dateName = ref('')
const dateConstraints = ref({
  [keyMap.dateType]: { value: 'date' },
  [keyMap.placeholder]: { value: '请选择日期' }
})

// 富文本类型示例
const richTextValue = ref('')
const richTextName = ref('')
const richTextConstraints = ref({
  [keyMap.rows]: { value: 4 },
  [keyMap.maxLength]: { value: 500 },
  [keyMap.placeholder]: { value: '请输入富文本内容' }
})

// 对象类型示例
const objectValue = ref('')
const objectName = ref('')
const objectOptions = ref({})
const objectConstraints = ref({
  [keyMap.placeholder]: { value: '请选择选项' }
})

// 上传类型示例
const attachmentValue = ref([])
const attachmentName = ref('')
const attachmentConstraints = ref({
  [keyMap.maxLength]: { value: 5 },
  [keyMap.maxSize]: { value: 1024 * 1024 * 10 },
  [keyMap.accept]: { value: 'image/*' }
})

// 调试信息
const debugInfo = computed(() => ({
  string: { value: stringValue.value, type: typeof stringValue.value, name: stringName.value },
  int: { value: intValue.value, type: typeof intValue.value, name: intName.value },
  float: { value: floatValue.value, type: typeof floatValue.value, name: floatName.value },
  boolean: { value: booleanValue.value, type: typeof booleanValue.value, name: booleanName.value },
  date: { value: dateValue.value, type: typeof dateValue.value, name: dateName.value },
  richText: { value: richTextValue.value, type: typeof richTextValue.value, name: richTextName.value },
  object: { value: objectValue.value, type: typeof objectValue.value, name: objectName.value, options: objectOptions.value },
  attachment: { value: attachmentValue.value, type: typeof attachmentValue.value, name: attachmentName.value }
}))
</script>

<style scoped lang="less">
.default-value-setter-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #409eff;
    margin-bottom: 30px;
    text-align: center;
  }

  h3 {
    color: #606266;
    margin-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }

  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fafafa;

    p {
      margin: 8px 0;
      font-size: 14px;
      color: #606266;
      
      &:first-of-type {
        font-weight: bold;
        color: #409eff;
      }
    }
  }

  .debug-section {
    margin-top: 40px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;

    pre {
      background-color: #fff;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      font-size: 12px;
      line-height: 1.5;
      overflow-x: auto;
    }
  }
}
</style>

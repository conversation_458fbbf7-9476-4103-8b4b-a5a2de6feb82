<script setup lang="ts">
import { constArr, EnumConfig, sourceEnum } from '@/views/system-configuration/Config/help'
import { scrollProp } from '@/plugins/vxeTable'
import { VxeGridInstance, VxeGridProps } from 'vxe-table'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { queryItemByClassificationName } from '@/api/systemConfiguration/Enum'
import { getMaterialCategoryList } from '@/views/basic-library-manage/material-library/api/materialCategory'
import { getProductCategoryList } from '@/views/basic-library-manage/product-library/api/product-category'
import { EnumApi } from '@/api/systemConfiguration/Enum'
import { ElMessage, ElRadio, ElRadioGroup } from 'element-plus'
import { getDictByCodeApi, queryCascade } from '@/api/common'
import { listToTree } from '@/utils/tree'

const enumItemTableRef = ref<VxeGridInstance>()
const maxHeight = useTableHeight({ tableRef: enumItemTableRef })
const enumItemLoading = ref(false)
const enumItemList = ref([])
const props = defineProps<{
  modelValue: boolean
}>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: EnumApi.Row): void
}>()
const formData = reactive({
  name: '',
  source: sourceEnum.enum
})
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      height: maxHeight.value - 55,
      loading: enumItemLoading.value,
      data: enumItemList.value,
      treeConfig: { children: 'childList', reserve: true },
      rowConfig: { keyField: 'id' },
      columns:
        formData.source === sourceEnum.enum
          ? EnumConfig.EnumSelectConfig
          : formData.source === sourceEnum.category
          ? EnumConfig.ProductConfig
          : EnumConfig.MaterialConfig,
      ...scrollProp
    } as VxeGridProps)
)
// 枚举
const fetchEnumItems = async () => {
  enumItemLoading.value = true
  const [error, result] = await queryItemByClassificationName({ source: 'SOURCE' })
  if (!error && result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}

//产品分类
const fetchProductType = async () => {
  enumItemLoading.value = true
  const result = await queryCascade({ type: sourceEnum.category, value: '' })
  console.log(result)
  if (result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}
//材料分类
const fetchMaterial = async () => {
  enumItemLoading.value = true
  const result = await queryCascade({ type: sourceEnum.material, value: '' })
  if (result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}
const handleClose = () => {
  emit('update:modelValue', false)
}

// 查找节点的第一层祖先节点
const findFirstLevelAncestor = (node, allNodes) => {
  // 如果当前节点就是第一层节点，直接返回
  const parentId = node.parentId || node.parent_id || node.pid
  if (!parentId || parentId === 0 || parentId === '0' || parentId === '') {
    return node
  }

  // 递归查找父节点
  const findParent = (currentNode) => {
    const currentParentId = currentNode.parentId || currentNode.parent_id || currentNode.pid

    // 如果当前节点是第一层节点，返回它
    if (
      !currentParentId ||
      currentParentId === 0 ||
      currentParentId === '0' ||
      currentParentId === ''
    ) {
      return currentNode
    }

    // 在所有节点中查找父节点
    const parentNode = findNodeInTree(allNodes, currentParentId)
    if (parentNode) {
      return findParent(parentNode)
    }

    // 如果找不到父节点，返回当前节点（可能数据有问题）
    return currentNode
  }

  return findParent(node)
}

// 在树形数据中查找指定ID的节点
const findNodeInTree = (nodes, targetId) => {
  for (const node of nodes) {
    // 检查当前节点
    if (node.id === targetId || node.categoryCode === targetId || node.code === targetId) {
      return node
    }

    // 递归检查子节点
    if (node.childList && node.childList.length > 0) {
      const found = findNodeInTree(node.childList, targetId)
      if (found) return found
    }

    // 检查其他可能的子节点字段
    if (node.children && node.children.length > 0) {
      const found = findNodeInTree(node.children, targetId)
      if (found) return found
    }
  }
  return null
}

// 检查节点是否有祖先节点在选中列表中
const hasAncestorInSelected = (node, selectedNodes, allNodes) => {
  const selectedNodeIds = new Set(selectedNodes.map((n) => n.id || n.categoryCode || n.code))

  // 递归查找父节点
  const checkParent = (currentNode) => {
    const parentId = currentNode.parentId || currentNode.parent_id || currentNode.pid

    // 如果没有父节点，说明已经到根节点
    if (!parentId || parentId === 0 || parentId === '0' || parentId === '') {
      return false
    }

    // 检查父节点是否在选中列表中
    if (selectedNodeIds.has(parentId)) {
      return true
    }

    // 查找父节点对象，继续向上检查
    const parentNode = findNodeInTree(allNodes, parentId)
    if (parentNode) {
      return checkParent(parentNode)
    }

    return false
  }

  return checkParent(node)
}
const handleSubmit = () => {
  let checked = []

  // 获取选中的记录
  if (formData.source === sourceEnum.enum) {
    // 枚举类型使用单选
    const row = enumItemTableRef.value?.getRadioRecord()
    if (!row) {
      ElMessage.warning('请选择来源')
      return
    }
    checked.push(row)
  } else {
    // 分类类型使用多选
    const rows = enumItemTableRef.value?.getCheckboxRecords() || []
    if (rows.length === 0) {
      ElMessage.warning('请选择来源')
      return
    }
    checked = rows
  }

  // 过滤选中的节点：如果选中的节点有祖先节点也被选中，则排除该节点，只保留祖先节点
  const filteredNodes = []

  if (formData.source === sourceEnum.enum) {
    // 枚举类型通常没有层级关系，直接返回
    filteredNodes.push(...checked)
  } else {
    // 分类类型：过滤掉有祖先节点也被选中的节点
    checked.forEach((selectedNode) => {
      // 检查当前节点是否有祖先节点也在选中列表中
      const hasSelectedAncestor = hasAncestorInSelected(selectedNode, checked, enumItemList.value)

      // 如果没有祖先节点被选中，则保留当前节点
      if (!hasSelectedAncestor) {
        filteredNodes.push(selectedNode)
      }
    })
  }

  // 提取过滤后节点的 code 编码集合
  const filteredCodes = filteredNodes
    .map((item) => {
      if (formData.source === sourceEnum.enum) {
        return item.code
      } else {
        // 分类类型使用 categoryCode
        return item.categoryCode || item.code
      }
    })
    .filter((code) => code) // 过滤掉空值

  console.log('原始选中的节点:', checked)
  console.log('过滤后的节点（排除有祖先被选中的节点）:', filteredNodes)
  console.log('过滤后节点的code集合:', filteredCodes)

  // 显示过滤逻辑的详细信息
  if (formData.source !== sourceEnum.enum) {
    console.log('节点过滤详情:')
    checked.forEach((selectedNode) => {
      const hasAncestor = hasAncestorInSelected(selectedNode, checked, enumItemList.value)
      const status = hasAncestor ? '❌ 排除（有祖先被选中）' : '✅ 保留'
      console.log(
        `  ${selectedNode.categoryCnName || selectedNode.name} (${
          selectedNode.categoryCode || selectedNode.code
        }) - ${status}`
      )
    })
  }
  // 关闭对话框并提交结果
  emit('update:modelValue', false)
  emit('submit', {
    type: formData.source,
    codes: filteredCodes, // 返回code集合
    nodes: filteredNodes, // 返回完整的节点信息
    // 兼容原有的单个值格式
    value: filteredCodes.length > 0 ? filteredCodes[0] : '',
    label:
      filteredNodes.length > 0
        ? filteredNodes[0].name || filteredNodes[0].categoryCnName || filteredNodes[0].label
        : ''
  })
}
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      formData.source = sourceEnum.enum
      fetchEnumItems()
    }
  }
)
watch(
  () => formData.source,
  () => {
    if (formData.source === sourceEnum.enum) {
      fetchEnumItems()
    } else if (formData.source === sourceEnum.category) {
      fetchProductType()
    } else if (formData.source === sourceEnum.material) {
      fetchMaterial()
    }
  }
)
</script>
<template>
  <Dialog v-model="visible" :before-close="handleClose" title="添加来源" width="800px">
    <template #header>
      <div>添加来源</div>
    </template>
    <ApiSelect
      v-model="formData.source"
      v-bind="{
        multiple: false,
        component: ElRadioGroup,
        childComponent: ElRadio,
        apiConfig: {
          api: getDictByCodeApi,
          config: { label: 'label', value: 'value' }
        },
        params: { dictCode: constArr.source }
      }"
    />
    <div>
      <VxeGrid ref="enumItemTableRef" v-bind="tableOptions" />
    </div>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>

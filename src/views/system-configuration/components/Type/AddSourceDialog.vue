<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElRadio, ElRadioGroup, ElButton } from 'element-plus'
import { constArr, EnumConfig, sourceEnum } from '@/views/system-configuration/Config/help'
import { scrollProp } from '@/plugins/vxeTable'
import { VxeGridInstance, VxeGridProps, VxeGrid } from 'vxe-table'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { queryItemByClassificationName } from '@/api/systemConfiguration/Enum'
import { getMaterialCategoryList } from '@/views/basic-library-manage/material-library/api/materialCategory'
import { getProductCategoryList } from '@/views/basic-library-manage/product-library/api/product-category'
import { EnumApi } from '@/api/systemConfiguration/Enum'
import { getDictByCodeApi, queryCascade } from '@/api/common'
import { listToTree } from '@/utils/tree'
import { ApiSelect } from '@/components/ApiSelect'
import { Dialog } from '@/components/Dialog'

const enumItemTableRef = ref<VxeGridInstance>()
const maxHeight = useTableHeight({ tableRef: enumItemTableRef })
const enumItemLoading = ref(false)
const enumItemList = ref([])
const props = defineProps<{
  modelValue: boolean
}>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: EnumApi.Row): void
}>()
const formData = reactive({
  name: '',
  source: sourceEnum.enum
})
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      height: maxHeight.value - 55,
      loading: enumItemLoading.value,
      data: enumItemList.value,
      treeConfig: { children: 'childList', reserve: true },
      rowConfig: { keyField: 'id' },
      columns:
        formData.source === sourceEnum.enum
          ? EnumConfig.EnumSelectConfig
          : formData.source === sourceEnum.category
          ? EnumConfig.ProductConfig
          : EnumConfig.MaterialConfig,
      ...scrollProp
    } as VxeGridProps)
)
// 枚举
const fetchEnumItems = async () => {
  enumItemLoading.value = true
  const [error, result] = await queryItemByClassificationName({ source: 'SOURCE' })
  if (!error && result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}

//产品分类
const fetchProductType = async () => {
  enumItemLoading.value = true
  const result = await queryCascade({ type: sourceEnum.category, value: '' })
  console.log(result)
  if (result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}
//材料分类
const fetchMaterial = async () => {
  enumItemLoading.value = true
  const result = await queryCascade({ type: sourceEnum.material, value: '' })
  if (result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}
const handleClose = () => {
  emit('update:modelValue', false)
}

const handleSubmit = () => {
  let checked = []

  // 获取选中的记录
  if (formData.source === sourceEnum.enum) {
    // 枚举类型使用单选
    const row = enumItemTableRef.value?.getRadioRecord()
    if (!row) {
      ElMessage.warning('请选择来源')
      return
    }
    checked.push(row)
  } else {
    // 分类类型使用多选
    const rows = enumItemTableRef.value?.getCheckboxRecords() || []
    if (rows.length === 0) {
      ElMessage.warning('请选择来源')
      return
    }
    checked = rows
  }

  // 简单过滤：排除有父节点也被选中的子节点
  const filteredNodes = []

  if (formData.source === sourceEnum.enum) {
    // 枚举类型直接返回
    filteredNodes.push(...checked)
  } else {
    // 创建选中节点的ID集合
    const selectedIds = new Set()
    checked.forEach((node) => {
      const nodeId = node.id || node.categoryCode || node.code
      if (nodeId) selectedIds.add(nodeId)
    })

    // 过滤：如果节点的父节点也被选中，就排除该节点
    checked.forEach((node) => {
      const parentId = node.parentId || node.parent_id || node.pid
      // 如果没有父节点，或父节点没有被选中，就保留该节点
      if (
        !parentId ||
        parentId === 0 ||
        parentId === '0' ||
        parentId === '' ||
        !selectedIds.has(parentId)
      ) {
        filteredNodes.push(node)
      }
    })
  }
  // 提取过滤后节点的 code 编码集合
  const filteredCodes = filteredNodes
    .map((item) => {
      if (formData.source === sourceEnum.enum) {
        return item.code
      } else {
        // 分类类型使用 categoryCode
        return item.categoryCode || item.code
      }
    })
    .filter((code) => code) // 过滤掉空值
  console.log('原始选中的节点:', checked)
  console.log('过滤后的节点:', filteredNodes)
  console.log('最终的code集合:', filteredCodes)

  // 关闭对话框并提交结果
  emit('update:modelValue', false)
  emit('submit', {
    type: formData.source,
    value: filteredCodes.length > 0 ? filteredCodes[0] : '', // 兼容原有格式，使用第一个值
    label:
      filteredNodes.length > 0
        ? filteredNodes[0].name || filteredNodes[0].categoryCnName || filteredNodes[0].label
        : '',
    codes: filteredCodes, // 新增：code集合
    nodes: filteredNodes // 新增：完整节点信息
  })
}
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      formData.source = sourceEnum.enum
      fetchEnumItems()
    }
  }
)

watch(
  () => formData.source,
  () => {
    if (formData.source === sourceEnum.enum) {
      fetchEnumItems()
    } else if (formData.source === sourceEnum.category) {
      fetchProductType()
    } else if (formData.source === sourceEnum.material) {
      fetchMaterial()
    }
  }
)
</script>
<template>
  <Dialog v-model="visible" :before-close="handleClose" title="添加来源" width="800px">
    <template #header>
      <div>添加来源</div>
    </template>
    <ApiSelect
      v-model="formData.source"
      v-bind="{
        multiple: false,
        component: ElRadioGroup,
        childComponent: ElRadio,
        apiConfig: {
          api: getDictByCodeApi,
          config: { label: 'label', value: 'value' }
        },
        params: { dictCode: constArr.source }
      }"
    />
    <div>
      <VxeGrid ref="enumItemTableRef" v-bind="tableOptions" />
    </div>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>

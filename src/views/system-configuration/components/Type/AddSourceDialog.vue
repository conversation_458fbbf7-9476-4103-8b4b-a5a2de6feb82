<script setup lang="ts">
import { constArr, EnumConfig, sourceEnum } from '@/views/system-configuration/Config/help'
import { scrollProp } from '@/plugins/vxeTable'
import { VxeGridInstance, VxeGridProps } from 'vxe-table'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { queryItemByClassificationName } from '@/api/systemConfiguration/Enum'
import { getMaterialCategoryList } from '@/views/basic-library-manage/material-library/api/materialCategory'
import { getProductCategoryList } from '@/views/basic-library-manage/product-library/api/product-category'
import { EnumApi } from '@/api/systemConfiguration/Enum'
import { ElMessage, ElRadio, ElRadioGroup } from 'element-plus'
import { getDictByCodeApi, queryCascade } from '@/api/common'

const enumItemTableRef = ref<VxeGridInstance>()
const maxHeight = useTableHeight({ tableRef: enumItemTableRef })
const enumItemLoading = ref(false)
const enumItemList = ref([])
const props = defineProps<{
  modelValue: boolean
}>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: EnumApi.Row): void
}>()
const formData = reactive({
  name: '',
  source: sourceEnum.enum
})
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      height: maxHeight.value - 55,
      loading: enumItemLoading.value,
      data: enumItemList.value,
      treeConfig: { children: 'childList', reserve: true },
      rowConfig: { keyField: 'id' },
      columns:
        formData.source === sourceEnum.enum
          ? EnumConfig.EnumSelectConfig
          : formData.source === sourceEnum.category
          ? EnumConfig.ProductConfig
          : EnumConfig.MaterialConfig,
      ...scrollProp
    } as VxeGridProps)
)
// 枚举
const fetchEnumItems = async () => {
  enumItemLoading.value = true
  const [error, result] = await queryItemByClassificationName({ source: 'SOURCE' })
  if (!error && result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}

//产品分类
const fetchProductType = async () => {
  enumItemLoading.value = true
  const result = await queryCascade({ type: sourceEnum.category, value: '' })
  console.log(result)
  if (result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}
//材料分类
const fetchMaterial = async () => {
  enumItemLoading.value = true
  const result = await queryCascade({ type: sourceEnum.material, value: '' })
  if (result.data) {
    enumItemList.value = result?.data || []
  }
  enumItemLoading.value = false
}
const handleClose = () => {
  emit('update:modelValue', false)
}
const buildParentMap = (treeData, parent = null, map = new Map()) => {
  treeData.forEach((node) => {
    if (parent) map.set(node.id, parent) // 存储父子关系

    if (node.children) {
      buildParentMap(node.children, node, map) // 递归处理子节点
    }
  })
  return map
}
const handleSubmit = () => {
  let checked = []
  if (sourceEnum.enum == formData.source) {
    const row = enumItemTableRef.value?.getRadioRecord()
    checked.push(row)
  } else {
    const row = enumItemTableRef.value?.getCheckboxRecords() || []
    console.log(row, 'row')
    checked = row
  }
  const parentMap = buildParentMap(checked)
  console.log(parentMap)
  return false
  console.log(checked)
  if (checked.length == 0) {
    ElMessage.warning('请选择来源')
    return
  }
  emit('update:modelValue', false)
  emit('submit', {
    type: formData.source,
    value: formData.source == sourceEnum.enum ? row.code : row.categoryCode
  })
  const selected = enumItemTableRef.value?.getCheckboxRecords()
  const row = enumItemTableRef.value?.getRadioRecord()
  if (!row) {
    ElMessage.warning('请选择来源')
    return
  }
  emit('update:modelValue', false)
  emit('submit', {
    type: formData.source,
    value: formData.source == sourceEnum.enum ? row.code : row.categoryCode
  })
}
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      formData.source = sourceEnum.enum
      fetchEnumItems()
    }
  }
)
watch(
  () => formData.source,
  () => {
    if (formData.source === sourceEnum.enum) {
      fetchEnumItems()
    } else if (formData.source === sourceEnum.category) {
      fetchProductType()
    } else if (formData.source === sourceEnum.material) {
      fetchMaterial()
    }
  }
)
</script>
<template>
  <Dialog v-model="visible" :before-close="handleClose" title="添加来源" width="800px">
    <template #header>
      <div>添加来源</div>
    </template>
    <ApiSelect
      v-model="formData.source"
      v-bind="{
        multiple: false,
        component: ElRadioGroup,
        childComponent: ElRadio,
        apiConfig: {
          api: getDictByCodeApi,
          config: { label: 'label', value: 'value' }
        },
        params: { dictCode: constArr.source }
      }"
    />
    <div>
      <VxeGrid ref="enumItemTableRef" v-bind="tableOptions" />
    </div>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>

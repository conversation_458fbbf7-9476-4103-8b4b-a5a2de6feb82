<script setup lang="ts">
import { TypeApi } from '@/api/systemConfiguration/type'
import { getConstraint, updateConstraintInstance } from '@/api/systemConfiguration/type'
import {
  constArr,
  componentRap,
  keyMap,
  constraintTypeMap
} from '@/views/system-configuration/Config/help'
import type { FormInstance, FormRules } from 'element-plus'
import AddSourceDialog from '@/views/system-configuration/components/Type/AddSourceDialog.vue'
import { ApiSelect } from '@/components/ApiSelect'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { getDictByCodeApi } from '@/api/common'
import DefaultValueSetter from '@/views/system-configuration/components/Type/DefaultValueSetter.vue'
const props = defineProps<{
  constraintAttributeId: number
  constraintType: string
  constraintAttribute: string
  name: string
  defaultProperty: boolean // 是否是默认的属性，只能修改默认值
  isExtends: boolean
  relationId?: string
}>()
const loading = ref(false)
const saveLoading = ref(false)
const constraintList = ref<TypeApi.PlmBaseConstraintInstanceResp[]>([])
const instanceId = ref()
const formRef = ref<FormInstance>()
const contentRef = ref()
const formData = ref({
  constraintDefaultValue: '',
  constraintDefaultName: '',
  options: {}
}) // 用于表单绑定的数据对象
const activeName = ref('constraint')

// 获取约束数据
const fetachContaint = async () => {
  if (!props.constraintType) {
    ElMessage.error('约束类型不正确')
    return
  }
  loading.value = true
  const [error, result] = await getConstraint({
    constraintAttributeId: props.relationId,
    constraintType: props.constraintType,
    constraintAttribute: props.constraintAttribute
  })
  if (!error && result?.data) {
    constraintList.value =
      (result.data?.constraintValue && JSON.parse(result.data?.constraintValue)) || []
    instanceId.value = result.data?.constraintAttributeId
    // 初始化表单数据
    initFormData(result.data)
  }
  loading.value = false
}

// 初始化表单数据
const initFormData = (content) => {
  const data = {}
  Object.keys(constraintList.value).forEach((item) => {
    data[item] = constraintList.value[item]?.value
  })

  formData.value = {
    ...data,
    constraintDefaultValue: getValueFormat(content),
    constraintDefaultName: content.constraintDefaultName
  }
}
const getValueFormat = (content) => {
  // 初始化是JSON字符串的内容
  try {
    const obj = JSON.parse(content.constraintDefaultValue)
    if (typeof obj == 'object' && obj) {
      return obj
    }
    return obj
  } catch (error) {
    return content.constraintDefaultValue
  }
}

// 监听属性ID变化，重新获取数据
watch(
  () => props,
  async () => {
    await fetachContaint()
  },
  {
    deep: true,
    immediate: true
  }
)

// 监听表单数据变化，同步到constraintList
watch(
  () => formData.value,
  (newVal) => {
    if (!newVal) return
    formateConstraintValue()
    Object.keys(newVal).forEach((key) => {
      const item = Object.values(constraintList.value).find((i) => i.key === key)
      if (item) {
        item.value = newVal[key]
      }
    })
  },
  { deep: true }
)
// 对constraintValue进行类型转换，供给默认值使用
const formateConstraintValue = () => {
  //如果为区间、附件则为数组
  if (
    props.constraintType === constraintTypeMap.ATTACHMENT ||
    constraintList.value?.[keyMap.isRange]?.value
  ) {
    formData.value.constraintDefaultValue = []
  }
  const map = ['CASCADER', 'CHECKBOXGROUP']
  // 如果组件类型为单选，必定为字符串
  if (constraintList.value?.[keyMap.component]?.value === 'RADIOGROUP') {
    formData.value.constraintDefaultValue = ''
  }
  // 如果是多选或者组件类型为cascader、ElCheckbox、ElRadioGroup 则为数组
  if (
    constraintList.value?.[keyMap.multiple]?.value ||
    map.includes(constraintList.value?.[keyMap.component]?.value)
  ) {
    formData.value.constraintDefaultValue = []
  }
  formData.value.constraintDefaultValue = formData.value.constraintDefaultValue.toString()
}
// 保存配置
const saveConstaint = async () => {
  if (!props.defaultProperty) {
    if (!formRef.value) return
  }

  try {
    // 使用表单校验
    if (!props.defaultProperty) await formRef.value?.validate()

    saveLoading.value = true
    console.log(constraintList.value, 'constraintList.value')
    const [error, result] = await updateConstraintInstance({
      relationId: props.relationId,
      constraintAttribute: props.constraintAttribute,
      constrain: {
        constraintAttribute: props.constraintAttribute,
        constraintAttributeId: props.constraintAttributeId,
        constraintType: props.constraintType,
        constraintValue: JSON.stringify(constraintList.value),
        constraintDefaultValue:
          typeof formData.value.constraintDefaultValue == 'string'
            ? formData.value.constraintDefaultValue
            : JSON.stringify(formData.value.constraintDefaultValue),
        constraintDefaultName: formData.value.constraintDefaultName
      }
    })
    saveLoading.value = false
    if (error === null && result) {
      ElMessage.success('保存成功')
    }
  } catch (error) {
    console.error('表单校验失败', error)
  }
}

// 表单校验规则
const rules = computed<FormRules>(() => {
  const formRules: FormRules = {}

  // 定义需要配对校验的字段
  const minMaxPairs = [
    ['minLength', 'maxLength'], // 字符串长度范围
    ['min', 'max'], // 数值范围
    ['minRange', 'maxRange'] // 范围值
  ]
  // 为每个约束项生成校验规则
  Object.values(constraintList.value).forEach((item) => {
    const itemRules: any = []
    // 添加最大最小值配对校验
    for (const [minKey, maxKey] of minMaxPairs) {
      if (item.key === minKey) {
        // 最小值校验
        itemRules.push({
          validator: (rule, value, callback) => {
            const maxValue = formData.value[maxKey]
            if (maxValue !== undefined && Number(value) > Number(maxValue)) {
              callback(new Error(`${item.label}不能大于${maxKey}`))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        })
      } else if (item.key === maxKey) {
        // 最大值校验
        itemRules.push({
          validator: (rule, value, callback) => {
            const minValue = formData.value[minKey]
            if (minValue !== undefined && Number(value) < Number(minValue)) {
              callback(new Error(`${item.label}不能小于${minKey}`))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        })
      }
    }

    if (itemRules.length > 0) {
      formRules[item.key] = itemRules
    }
  })

  return formRules
})
// 选择来源
const sourceDialog = reactive({
  visible: false
})
const chooseSource = () => {
  sourceDialog.visible = true
}
//针对来源进行关联数据
const sourceRelate = (sourceData) => {
  sourceDialog.visible = false
  formData.value.options = sourceData
}
//单个值进行绑定
const bindItemProps = computed(() => (element) => {
  // 如果是链接，必须只读才能输入
  if (element.key === keyMap.link) {
    if (!constraintList.value[keyMap.readonly]?.value) {
      return {
        disabled: true
      }
    } else {
      return {
        disabled: false
      }
    }
  }
  // 如果是数字类型，需要绑定最小值和最大值
  if (componentRap[element.type.toUpperCase()]?.name === componentRap.NUMBER.name) {
    let min = constraintList.value[keyMap.minLength] || 0
    //行数最小为1
    element.key === keyMap.rows && (min = 1)
    return {
      min: min,
      max: constraintList.value[keyMap.maxLength]
    }
  }
})
const maxHeight = useTableHeight({ tableRef: contentRef })
</script>
<template>
  <div class="content">
    <p class="title">属性名称 - {{ name }}</p>
    <div
      ref="contentRef"
      :style="{ height: maxHeight - 280 + 'px' }"
      class="constraint"
      v-loading="loading"
    >
      <ElTabs v-model="activeName">
        <ElTabPane label="约束" v-if="!defaultProperty" name="constraint">
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="90px"
            label-position="right"
          >
            <ElFormItem
              v-for="item in constraintList"
              :key="item.key"
              :label="item.label"
              :prop="item.key"
            >
              <!--如果key是options--->
              <div v-if="item.key === 'options'">
                <ElButton @click="chooseSource" :disabled="isExtends">选择来源</ElButton>

                <span> {{ formData[item.key]?.label || formData[item.key]?.value || '' }}</span>
              </div>
              <!--              -如果是constArr里面配置的可选择的- -->

              <ApiSelect
                v-else-if="constArr[item.key]"
                v-model="formData[item.key]"
                :disabled="isExtends"
                v-bind="{
                  multiple: false,
                  component: ElSelect,
                  apiConfig: {
                    api: getDictByCodeApi,
                    config: {
                      label: 'label',
                      value: 'value'
                    }
                  },
                  params: {
                    dictCode: constArr[item.key]
                  },
                  clearable: true
                }"
              />
              <component
                v-else
                :disabled="isExtends"
                :is="componentRap[item.type.toUpperCase()]"
                v-model="formData[item.key]"
                v-bind="bindItemProps(item)"
              />
            </ElFormItem>
          </ElForm>
        </ElTabPane>
        <ElTabPane label="默认值" name="defaultValue">
          <ElForm v-if="activeName === 'defaultValue'" label-width="90px" label-position="right">
            <!-- 使用新的DefaultValueSetter组件 -->
            <DefaultValueSetter
              :constraintType="constraintType"
              :constraintList="constraintList"
              v-model:defaultValue="formData.constraintDefaultValue"
              v-model:defaultName="formData.constraintDefaultName"
              v-model:options="formData.options"
              :disabled="isExtends"
              :loading="loading"
            />
          </ElForm>
        </ElTabPane>
      </ElTabs>
    </div>
    <div class="flex justify-center">
      <ElButton
        v-if="!isExtends"
        class="mt-2"
        type="primary"
        @click="saveConstaint"
        :loading="saveLoading"
        >保存</ElButton
      >
    </div>
  </div>
  <AddSourceDialog @submit="sourceRelate" v-model="sourceDialog.visible" />
</template>

<style scoped lang="less">
.title {
  width: 100%;
  padding: 4px 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.constraint {
  height: calc(100% - 20px);
  padding: 10px;
  border: 1px solid #e5e7eb;

  .el-tabs {
    height: 100%;

    .el-tab-pane {
      height: 100%;
      overflow-y: auto;
    }
  }
}

.content {
  height: 90%;
}
</style>

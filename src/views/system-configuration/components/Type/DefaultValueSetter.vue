<script setup lang="ts">
import { ref, computed, onMounted, useAttrs } from 'vue'
import {
  ElMessage,
  ElSwitch,
  ElInput,
  ElDatePicker,
  ElInputNumber,
  ElAlert,
  ElButton,
  ElFormItem,
  ElSelect
} from 'element-plus'
import {
  constraintTypeMap,
  componentRap,
  keyMap,
  sourceEnum,
  componentType
} from '@/views/system-configuration/Config/help'
import { ApiSelect } from '@/components/ApiSelect'
import { getDictByCodeApi, queryCascade } from '@/api/common'
import OssUpload from '@/components/Upload/OssUpload.vue'
import AddSourceDialog from './AddSourceDialog.vue'

const props = defineProps({
  constraintType: {
    type: String,
    required: true
  },
  // 约束列表数据
  constraintList: {
    type: Object,
    required: true
  },
  // 当前默认值
  defaultValue: {
    type: [String, Number, Boolean, Array, Object],
    default: ''
  },
  // 当前默认别名
  defaultName: {
    type: String,
    default: ''
  },
  // 选项数据（用于下拉选择类型）
  options: {
    type: Object,
    default: () => ({})
  },
  // 是否禁用编辑
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:defaultValue', 'update:defaultName', 'update:options'])

const localDefaultValue = computed({
  get: () => props.defaultValue,
  set: (val) => emit('update:defaultValue', val)
})
const localDefaultName = computed({
  get: () => props.defaultName,
  set: (val) => emit('update:defaultName', val)
})

const localOptions = computed({
  get: () => props.options,
  set: (val) => emit('update:options', val)
})

// 根据约束类型确定要渲染的组件
const renderComponent = computed(() => {
  if (props.constraintType === constraintTypeMap.OBJECT) {
    return ApiSelect
  }

  // 确保组件存在，否则返回默认的 ElInput
  const component = componentRap[props.constraintType.toUpperCase()]
  if (!component) {
    console.warn(`未找到约束类型 ${props.constraintType} 对应的组件，使用默认的 ElInput`)
    return ElInput
  }

  return component
})

// 计算组件需要的属性
const bindProps = computed(() => {
  try {
    let propsMap = {}

    // 从约束列表中提取属性
    if (props.constraintList) {
      Object.keys(props.constraintList).forEach((key) => {
        if (key !== 'options' && key !== 'component') {
          propsMap[key] = props.constraintList[key]?.value
        }
      })
    }
    // 对于选择类型，配置API选择器
    if (props.constraintType === constraintTypeMap.OBJECT && localOptions.value?.value) {
      return {
        ...propsMap,
        apiConfig: {
          api: localOptions.value?.type === sourceEnum.enum ? getDictByCodeApi : queryCascade,
          config:
            localOptions.value?.type === sourceEnum.enum
              ? {
                  label: 'label',
                  value: 'value'
                }
              : {
                  label: 'categoryCnName',
                  value: 'categoryCode',
                  children: 'childList'
                }
        },
        params:
          localOptions.value?.type === sourceEnum.enum
            ? {
                dictCode: localOptions.value?.value
              }
            : {
                type: localOptions.value?.type,
                value: localOptions.value?.value
              },
        disabled: props.disabled,
        component: componentType?.[props.constraintList?.component?.value] || ElSelect,
        childComponent:
          props.constraintList?.component?.value === 'RADIOGROUP'
            ? ElRadio
            : props.constraintList?.component?.value === 'CHECKBOXGROUP'
            ? ElCheckbox
            : ''
      }
    }

    // 其他类型返回基本属性
    return {
      ...propsMap,
      disabled: props.disabled
    }
  } catch (error) {
    console.error('计算 bindProps 时出错:', error)
    return { disabled: props.disabled }
  }
})

// 根据约束类型获取适当的占位符文本
const getPlaceholder = computed(() => {
  const typeMap = {
    [constraintTypeMap.STRING]: '请输入文本',
    [constraintTypeMap.INT]: '请输入数字',
    [constraintTypeMap.FLOAT]: '请输入数字',
    [constraintTypeMap.RICH_TEXT]: '请输入内容',
    [constraintTypeMap.DATE_TIME]: '请选择日期',
    [constraintTypeMap.OBJECT]: '请选择选项'
  }

  return typeMap[props.constraintType] || '请输入'
})

// 选择来源对话框控制
const sourceDialogVisible = ref(false)

// 打开选择来源对话框
const openSourceDialog = () => {
  sourceDialogVisible.value = true
}

// 处理来源选择
const handleSourceSelect = (sourceData) => {
  localOptions.value = sourceData
  sourceDialogVisible.value = false
}
watch(
  () => props.options,
  () => {
    console.log('检测到')
  },
  {
    deep: true
  }
)
</script>

<template>
  <div class="default-value-setter">
    <!-- 根据约束类型渲染不同的输入组件 -->
    <div class="component-container">
      <ElFormItem label="默认值" v-if="constraintType !== constraintTypeMap.ATTACHMENT">
        <!-- 对象类型（下拉选择） -->
        <template v-if="constraintType === constraintTypeMap.OBJECT">
          <div class="source-selector">
            <ElButton @click="openSourceDialog" :disabled="disabled" size="small">
              选择数据来源
            </ElButton>
            <span class="source-value">{{ localOptions?.label || localOptions?.value }}</span>
          </div>
          <div style="width: 100%">
            <component
              v-if="localOptions?.value"
              :is="renderComponent"
              v-model="localDefaultValue"
              :key="JSON.stringify(constraintList)"
              v-bind="bindProps"
              :placeholder="getPlaceholder"
            />
            <ElAlert v-else-if="!localOptions?.value" type="info" :closable="false" show-icon>
              请先选择数据来源
            </ElAlert>
          </div>
        </template>
        <!-- 布尔类型（开关） -->
        <template v-else-if="constraintType === constraintTypeMap.BOOLEAN">
          <ElSwitch v-model="localDefaultValue" :disabled="disabled" />
        </template>

        <!-- 上传类型 -->
        <template v-else-if="constraintType === constraintTypeMap.ATTACHMENT">
          <OssUpload
            v-model="localDefaultValue"
            :limit="constraintList[keyMap.maxLength]?.value || 5"
            :size-limit="constraintList[keyMap.maxSize]?.value || 1024 * 1024 * 50"
            :accept="constraintList[keyMap.accept]?.value || 'image/*'"
            drag
            :listType="constraintList[keyMap.dataType]?.value === 'IMAGE' ? 'picture-card' : 'text'"
            :disabled="disabled"
          />
        </template>

        <!-- 富文本类型 -->
        <template v-else-if="constraintType === constraintTypeMap.RICH_TEXT">
          <ElInput
            type="textarea"
            v-model="localDefaultValue"
            :rows="constraintList[keyMap.rows]?.value || 3"
            :maxlength="constraintList[keyMap.maxLength]?.value"
            :show-word-limit="!!constraintList[keyMap.maxLength]?.value"
            :placeholder="getPlaceholder"
            :disabled="disabled"
          />
        </template>

        <!-- 日期时间类型 -->
        <template v-else-if="constraintType === constraintTypeMap.DATE_TIME">
          <ElDatePicker
            v-model="localDefaultValue"
            :type="constraintList[keyMap.isRange]?.value ? 'datetimerange' : 'date'"
            :placeholder="getPlaceholder"
            :format="constraintList[keyMap.format]?.value || 'YYYY-MM-DD'"
            :value-format="constraintList[keyMap.format]?.value || 'YYYY-MM-DD'"
            :disabled="disabled"
          />
        </template>

        <!-- 数字类型 -->
        <template
          v-else-if="
            constraintType === constraintTypeMap.INT || constraintType === constraintTypeMap.FLOAT
          "
        >
          <ElInputNumber
            v-model="localDefaultValue"
            :min="constraintList[keyMap.min]?.value"
            :max="constraintList[keyMap.max]?.value"
            :precision="
              constraintType === constraintTypeMap.FLOAT
                ? constraintList[keyMap.precision]?.value || 2
                : 0
            "
            :step="constraintList[keyMap.step]?.value || 1"
            :placeholder="getPlaceholder"
            :disabled="disabled"
          />
        </template>

        <!-- 默认文本类型 -->
        <template v-else>
          <component
            v-if="!loading"
            :is="renderComponent"
            v-model="localDefaultValue"
            v-bind="bindProps"
            :placeholder="getPlaceholder"
            :disabled="disabled"
        /></template>
      </ElFormItem>
    </div>

    <!-- 默认别名输入 -->
    <div class="default-name-container">
      <ElFormItem label="默认别名">
        <ElInput v-model="localDefaultName" placeholder="请输入默认别名" :disabled="disabled" />
      </ElFormItem>
    </div>

    <!-- 选择来源对话框 -->
    <AddSourceDialog v-model="sourceDialogVisible" @submit="handleSourceSelect" />
  </div>
</template>
<style scoped lang="less">
.default-value-setter {
  width: 100%;

  .component-container {
    margin-bottom: 16px;
  }

  .source-selector {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .source-value {
      margin-left: 12px;
      font-size: 14px;
      color: #606266;
    }
  }

  .default-name-container {
    margin-top: 16px;
  }
}
</style>

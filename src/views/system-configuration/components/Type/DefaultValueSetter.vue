<script setup lang="ts">
import { ref, computed, onMounted, useAttrs } from 'vue'
import {
  ElMessage,
  ElSwitch,
  ElInput,
  ElDatePicker,
  ElInputNumber,
  ElAlert,
  ElButton,
  ElFormItem
} from 'element-plus'
import {
  constraintTypeMap,
  componentRap,
  keyMap,
  sourceEnum
} from '@/views/system-configuration/Config/help'
import { ApiSelect } from '@/components/ApiSelect'
import { getDictByCodeApi, queryCascade } from '@/api/common'
import { omit } from 'xe-utils'
import OssUpload from '@/components/Upload/OssUpload.vue'
import AddSourceDialog from './AddSourceDialog.vue'

const props = defineProps({
  // 约束类型 (INPUT_STRING_TYPE, INPUT_NUMBER_TYPE, SWITCH_TYPE, UPLOAD_TYPE, SELECT_TYPE, TEXTAREA_TYPE, DATE_TYPE)
  constraintType: {
    type: String,
    required: true
  },
  // 约束列表数据
  constraintList: {
    type: Object,
    required: true
  },
  // 当前默认值
  defaultValue: {
    type: [String, Number, Boolean, Array, Object],
    default: ''
  },
  // 当前默认别名
  defaultName: {
    type: String,
    default: ''
  },
  // 选项数据（用于下拉选择类型）
  options: {
    type: Object,
    default: () => ({})
  },
  // 是否禁用编辑
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:defaultValue', 'update:defaultName', 'update:options'])

// 本地值，用于双向绑定
const localDefaultValue = computed({
  get: () => props.defaultValue,
  set: (val) => emit('update:defaultValue', val)
})

const localDefaultName = computed({
  get: () => props.defaultName,
  set: (val) => emit('update:defaultName', val)
})

const localOptions = computed({
  get: () => props.options,
  set: (val) => emit('update:options', val)
})

// 根据约束类型确定要渲染的组件
const renderComponent = computed(() => {
  if (props.constraintType === constraintTypeMap.OBJECT) {
    return ApiSelect
  }
  return componentRap[props.constraintType.toUpperCase()]
})

// 计算组件需要的属性
const bindProps = computed(() => {
  let propsMap = {}

  // 从约束列表中提取属性
  if (props.constraintList) {
    Object.keys(props.constraintList).forEach((key) => {
      propsMap[key] = props.constraintList[key]?.value
    })
  }
  // 如果没有选项值，返回基本属性
  if (!localOptions.value?.value) {
    return {
      ...omit(propsMap, ['options', 'component']),
      disabled: props.disabled
    }
  }

  // 对于选择类型，配置API选择器
  if (props.constraintType === constraintTypeMap.OBJECT) {
    return {
      apiConfig: {
        api: localOptions.value?.type === sourceEnum.enum ? getDictByCodeApi : queryCascade,
        config: {
          label: 'label',
          value: 'value'
        }
      },
      params:
        localOptions.value?.type === sourceEnum.enum
          ? {
              dictCode: localOptions.value?.value
            }
          : {
              type: localOptions.value?.type,
              value: localOptions.value?.value
            },
      ...omit(propsMap, ['options', 'component']),
      component: ElSelect,
      disabled: props.disabled
    }
  }

  // 其他类型返回基本属性
  return {
    ...propsMap,
    disabled: props.disabled
  }
})

// 根据约束类型获取适当的占位符文本
const getPlaceholder = computed(() => {
  const typeMap = {
    [constraintTypeMap.STRING]: '请输入文本',
    [constraintTypeMap.INT]: '请输入数字',
    [constraintTypeMap.FLOAT]: '请输入数字',
    [constraintTypeMap.RICH_TEXT]: '请输入内容',
    [constraintTypeMap.DATE_TIME]: '请选择日期',
    [constraintTypeMap.OBJECT]: '请选择选项'
  }

  return typeMap[props.constraintType] || '请输入'
})

// 处理不同类型的默认值格式化
const formatDefaultValue = (value) => {
  if (value === undefined || value === null) return ''

  // 根据约束类型格式化值
  switch (props.constraintType) {
    case constraintTypeMap.INT:
    case constraintTypeMap.FLOAT:
      return Number(value)
    case constraintTypeMap.BOOLEAN:
      return Boolean(value)
    case constraintTypeMap.OBJECT:
      // 对于对象类型，可能需要特殊处理
      return value
    default:
      return String(value)
  }
}

// 验证默认值是否符合约束条件
const validateDefaultValue = (value) => {
  if (value === undefined || value === null || value === '') return true

  try {
    // 数字类型验证
    if (
      props.constraintType === constraintTypeMap.INT ||
      props.constraintType === constraintTypeMap.FLOAT
    ) {
      const numValue = Number(value)

      // 检查最小值约束
      if (props.constraintList[keyMap.min]?.value !== undefined) {
        const min = Number(props.constraintList[keyMap.min].value)
        if (numValue < min) {
          ElMessage.warning(`值不能小于${min}`)
          return false
        }
      }

      // 检查最大值约束
      if (props.constraintList[keyMap.max]?.value !== undefined) {
        const max = Number(props.constraintList[keyMap.max].value)
        if (numValue > max) {
          ElMessage.warning(`值不能大于${max}`)
          return false
        }
      }

      // 对于整数类型，检查是否为整数
      if (props.constraintType === constraintTypeMap.INT && !Number.isInteger(numValue)) {
        ElMessage.warning('请输入整数')
        return false
      }
    }

    // 字符串类型验证
    if (
      props.constraintType === constraintTypeMap.STRING ||
      props.constraintType === constraintTypeMap.RICH_TEXT
    ) {
      const strValue = String(value)

      // 检查最小长度约束
      if (props.constraintList[keyMap.minLength]?.value !== undefined) {
        const minLength = Number(props.constraintList[keyMap.minLength].value)
        if (strValue.length < minLength) {
          ElMessage.warning(`长度不能小于${minLength}个字符`)
          return false
        }
      }

      // 检查最大长度约束
      if (props.constraintList[keyMap.maxLength]?.value !== undefined) {
        const maxLength = Number(props.constraintList[keyMap.maxLength].value)
        if (strValue.length > maxLength) {
          ElMessage.warning(`长度不能超过${maxLength}个字符`)
          return false
        }
      }

      // 检查正则表达式约束
      if (props.constraintList[keyMap.pattern]?.value) {
        const pattern = new RegExp(props.constraintList[keyMap.pattern].value)
        if (!pattern.test(strValue)) {
          ElMessage.warning('输入格式不符合要求')
          return false
        }
      }
    }

    // 上传类型验证
    if (props.constraintType === constraintTypeMap.ATTACHMENT) {
      // 这里可以添加对上传文件的验证逻辑
      // 例如文件数量、大小等
    }

    return true
  } catch (error) {
    console.error('验证默认值时出错:', error)
    return false
  }
}

// 处理值变更
const handleValueChange = (value) => {
  const formattedValue = formatDefaultValue(value)
  if (validateDefaultValue(formattedValue)) {
    localDefaultValue.value = formattedValue
  }
}

// 选择来源对话框控制
const sourceDialogVisible = ref(false)

// 打开选择来源对话框
const openSourceDialog = () => {
  sourceDialogVisible.value = true
}

// 处理来源选择
const handleSourceSelect = (sourceData) => {
  console.log('接收到的来源数据:', sourceData)

  // 处理新的数据格式，支持多个code的情况
  const displayLabel =
    sourceData.codes && sourceData.codes.length > 1
      ? `${sourceData.label} 等${sourceData.codes.length}项`
      : sourceData.label || sourceData.value

  localOptions.value = {
    label: displayLabel,
    value: sourceData.value, // 兼容原有格式，使用第一个值
    codes: sourceData.codes || [sourceData.value], // 新增：code集合
    nodes: sourceData.nodes || [], // 新增：完整节点信息
    type: sourceData.type
  }

  console.log('更新后的选项配置:', localOptions.value)
  sourceDialogVisible.value = false
}

// 组件挂载时初始化
onMounted(() => {
  // 如果有初始值，进行格式化
  if (props.defaultValue) {
    handleValueChange(props.defaultValue)
  }
})
</script>

<template>
  <div class="default-value-setter">
    <!-- 根据约束类型渲染不同的输入组件 -->
    <div class="component-container">
      <ElFormItem label="默认值">
        <!-- 对象类型（下拉选择） -->
        <template v-if="constraintType === constraintTypeMap.OBJECT">
          <div class="source-selector">
            <ElButton @click="openSourceDialog" :disabled="disabled" size="small">
              选择数据来源
            </ElButton>
            <span class="source-value">{{ localOptions?.label || localOptions?.value }}</span>
          </div>
          <component
            v-if="!loading && localOptions?.value"
            :is="renderComponent"
            v-model="localDefaultValue"
            :key="localOptions?.value"
            v-bind="bindProps"
            :placeholder="getPlaceholder"
            @change="handleValueChange"
          />
          <ElAlert v-else-if="!localOptions?.value" type="info" :closable="false" show-icon>
            请先选择数据来源
          </ElAlert>
        </template>

        <!-- 布尔类型（开关） -->
        <template v-else-if="constraintType === constraintTypeMap.BOOLEAN">
          <ElSwitch v-model="localDefaultValue" :disabled="disabled" @change="handleValueChange" />
        </template>

        <!-- 上传类型 -->
        <template v-else-if="constraintType === constraintTypeMap.ATTACHMENT">
          <OssUpload
            v-model="localDefaultValue"
            :limit="constraintList[keyMap.maxLength]?.value || 5"
            :size-limit="constraintList[keyMap.maxSize]?.value || 1024 * 1024 * 50"
            accept="image/*"
            drag
            listType="picture-card"
            multiple
            :disabled="disabled"
          />
        </template>

        <!-- 富文本类型 -->
        <template v-else-if="constraintType === constraintTypeMap.RICH_TEXT">
          <ElInput
            v-model="localDefaultValue"
            type="textarea"
            :rows="constraintList[keyMap.rows]?.value || 3"
            :maxlength="constraintList[keyMap.maxLength]?.value"
            :show-word-limit="!!constraintList[keyMap.maxLength]?.value"
            :placeholder="getPlaceholder"
            :disabled="disabled"
            @input="handleValueChange"
          />
        </template>

        <!-- 日期时间类型 -->
        <template v-else-if="constraintType === constraintTypeMap.DATE_TIME">
          <ElDatePicker
            v-model="localDefaultValue"
            :type="constraintList[keyMap.dateType]?.value || 'date'"
            :placeholder="getPlaceholder"
            :disabled="disabled"
            @change="handleValueChange"
          />
        </template>

        <!-- 数字类型 -->
        <template
          v-else-if="
            constraintType === constraintTypeMap.INT || constraintType === constraintTypeMap.FLOAT
          "
        >
          <ElInputNumber
            v-model="localDefaultValue"
            :min="constraintList[keyMap.min]?.value"
            :max="constraintList[keyMap.max]?.value"
            :precision="
              constraintType === constraintTypeMap.FLOAT
                ? constraintList[keyMap.precision]?.value || 2
                : 0
            "
            :step="constraintList[keyMap.step]?.value || 1"
            :placeholder="getPlaceholder"
            :disabled="disabled"
            @change="handleValueChange"
          />
        </template>

        <!-- 默认文本类型 -->
        <template v-else>
          <component
            v-if="!loading"
            :is="renderComponent"
            v-model="localDefaultValue"
            v-bind="bindProps"
            :placeholder="getPlaceholder"
            :disabled="disabled"
            @input="handleValueChange"
        /></template>
      </ElFormItem>
    </div>

    <!-- 默认别名输入 -->
    <div class="default-name-container">
      <ElFormItem label="默认别名">
        <ElInput v-model="localDefaultName" placeholder="请输入默认别名" :disabled="disabled" />
      </ElFormItem>
    </div>

    <!-- 选择来源对话框 -->
    <AddSourceDialog v-model="sourceDialogVisible" @submit="handleSourceSelect" />
  </div>
</template>
<style scoped lang="less">
.default-value-setter {
  width: 100%;

  .component-container {
    margin-bottom: 16px;
  }

  .source-selector {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .source-value {
      margin-left: 12px;
      font-size: 14px;
      color: #606266;
    }
  }

  .default-name-container {
    margin-top: 16px;
  }
}
</style>

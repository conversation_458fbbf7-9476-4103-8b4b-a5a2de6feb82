<template>
  <div class="component-test">
    <h2>组件测试页面</h2>
    
    <div class="test-section">
      <h3>1. DefaultValueSetter 组件测试</h3>
      <DefaultValueSetter
        :constraintType="constraintTypeMap.STRING"
        :constraintList="testConstraints"
        v-model:defaultValue="testValue"
        v-model:defaultName="testName"
      />
      <p>当前值: {{ testValue }}</p>
      <p>当前名称: {{ testName }}</p>
    </div>

    <div class="test-section">
      <h3>2. AddSourceDialog 组件测试</h3>
      <ElButton @click="openDialog">打开选择来源对话框</ElButton>
      <div v-if="selectedData">
        <p>选择结果: {{ selectedData }}</p>
      </div>
      <AddSourceDialog
        v-model="dialogVisible"
        @submit="handleDialogSubmit"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton } from 'element-plus'
import { constraintTypeMap, keyMap } from '@/views/system-configuration/Config/help'
import DefaultValueSetter from './DefaultValueSetter.vue'
import AddSourceDialog from './AddSourceDialog.vue'

// DefaultValueSetter 测试数据
const testValue = ref('')
const testName = ref('')
const testConstraints = ref({
  [keyMap.minLength]: { value: 2 },
  [keyMap.maxLength]: { value: 50 },
  [keyMap.placeholder]: { value: '请输入测试文本' }
})

// AddSourceDialog 测试数据
const dialogVisible = ref(false)
const selectedData = ref(null)

const openDialog = () => {
  dialogVisible.value = true
}

const handleDialogSubmit = (data) => {
  selectedData.value = data
  console.log('选择的数据:', data)
}
</script>

<style scoped lang="less">
.component-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #409eff;
    margin-bottom: 30px;
    text-align: center;
  }

  h3 {
    color: #606266;
    margin-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }

  .test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fafafa;

    p {
      margin: 8px 0;
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>

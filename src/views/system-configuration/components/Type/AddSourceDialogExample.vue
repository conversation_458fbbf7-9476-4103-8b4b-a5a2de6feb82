<template>
  <div class="add-source-dialog-example">
    <h2>AddSourceDialog 组件使用示例</h2>
    
    <div class="example-section">
      <h3>选择数据来源</h3>
      <ElButton type="primary" @click="openDialog">打开选择来源对话框</ElButton>
      
      <div v-if="selectedSource" class="result-section">
        <h4>选择结果：</h4>
        <div class="result-item">
          <strong>数据源类型：</strong> {{ getSourceTypeName(selectedSource.type) }}
        </div>
        <div class="result-item">
          <strong>第一层选中的code集合：</strong> 
          <ElTag v-for="code in selectedSource.codes" :key="code" class="code-tag">
            {{ code }}
          </ElTag>
        </div>
        <div class="result-item">
          <strong>选中节点数量：</strong> {{ selectedSource.nodes.length }}
        </div>
        <div class="result-item">
          <strong>显示标签：</strong> {{ selectedSource.label }}
        </div>
        <div class="result-item">
          <strong>兼容值：</strong> {{ selectedSource.value }}
        </div>
      </div>

      <div v-if="selectedSource && selectedSource.nodes.length > 0" class="nodes-section">
        <h4>选中的第一层节点详情：</h4>
        <ElTable :data="selectedSource.nodes" border style="width: 100%">
          <ElTableColumn prop="id" label="ID" width="80" />
          <ElTableColumn prop="code" label="编码" width="120" />
          <ElTableColumn prop="categoryCode" label="分类编码" width="120" />
          <ElTableColumn prop="name" label="名称" width="150" />
          <ElTableColumn prop="categoryCnName" label="中文名称" width="150" />
          <ElTableColumn prop="categoryEnName" label="英文名称" width="150" />
          <ElTableColumn prop="parentId" label="父级ID" width="100" />
          <ElTableColumn label="操作" width="100">
            <template #default="{ row }">
              <ElButton size="small" @click="showNodeDetail(row)">详情</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
    </div>

    <!-- 选择来源对话框 -->
    <AddSourceDialog
      v-model="dialogVisible"
      @submit="handleSourceSelect"
    />

    <!-- 节点详情对话框 -->
    <ElDialog v-model="nodeDetailVisible" title="节点详情" width="600px">
      <pre>{{ JSON.stringify(selectedNode, null, 2) }}</pre>
      <template #footer>
        <ElButton @click="nodeDetailVisible = false">关闭</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElTag, ElTable, ElTableColumn, ElDialog } from 'element-plus'
import { sourceEnum } from '@/views/system-configuration/Config/help'
import AddSourceDialog from './AddSourceDialog.vue'

// 对话框控制
const dialogVisible = ref(false)
const nodeDetailVisible = ref(false)

// 选择结果
const selectedSource = ref(null)
const selectedNode = ref(null)

// 打开对话框
const openDialog = () => {
  dialogVisible.value = true
}

// 处理来源选择
const handleSourceSelect = (sourceData) => {
  console.log('选择的来源数据:', sourceData)
  selectedSource.value = sourceData
}

// 显示节点详情
const showNodeDetail = (node) => {
  selectedNode.value = node
  nodeDetailVisible.value = true
}

// 获取数据源类型名称
const getSourceTypeName = (type) => {
  const typeMap = {
    [sourceEnum.enum]: '枚举类型',
    [sourceEnum.category]: '产品分类',
    [sourceEnum.material]: '材料分类'
  }
  return typeMap[type] || '未知类型'
}
</script>

<style scoped lang="less">
.add-source-dialog-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #409eff;
    margin-bottom: 30px;
    text-align: center;
  }

  h3 {
    color: #606266;
    margin-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }

  h4 {
    color: #909399;
    margin: 20px 0 10px 0;
  }

  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .result-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;

    .result-item {
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 1.5;

      strong {
        color: #409eff;
        margin-right: 8px;
      }

      .code-tag {
        margin-right: 8px;
        margin-bottom: 4px;
      }
    }
  }

  .nodes-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
  }

  pre {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    font-size: 12px;
    line-height: 1.5;
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>

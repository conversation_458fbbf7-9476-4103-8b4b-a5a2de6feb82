<script lang="tsx">
import {
  ElButton,
  ElCol,
  ElCollapse,
  ElCollapseItem,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElLoading,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElRow,
  ElSelect,
  ElSelectV2,
  FormInstance,
  FormRules,
  UploadRawFile
} from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import RGBInput from '@/views/basic-library-manage/color-library/components/RGBInput.vue'
import BaseUpload from '@/components/Upload/BaseUpload.vue'
import { UploadValue } from '@/components/Upload/types'
import { debounce } from 'lodash-es'
import dom2image from 'dom-to-image'
import { VxeColumn, VxeTable, VxeTableInstance } from 'vxe-table'
import {
  ColorInfoAPI,
  ColorRGBListAPI,
  createColor,
  editColor,
  getColorRGBList,
  MaterialByColorIdAPI,
  queryMaterialByColor,
  viewColor,
  viewColorByVersionId
} from '@/views/basic-library-manage/color-library/api/colorInfo'
import {
  colorLibraryConst,
  ColorLibraryEnum,
  COMB_COLOR
} from '@/views/basic-library-manage/color-library/const'
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import { UPLOAD_URL, uploadFiles } from '@/views/basic-library-manage/api/upload'
import {
  ColorCategoryListAPI,
  ColorListPageAPI,
  getColorCategoryList
} from '@/views/basic-library-manage/color-library/api/color-list'
import ElColorPicker from '@/views/basic-library-manage/color-library/components/ColorPicker.vue'
import { useClosePage } from '@/hooks/autoImport/useClose'
import { Icon } from '@/components/Icon'

export default defineComponent({
  name: 'ColorInfo',
  components: {
    Icon,
    ElColorPicker
  },
  setup() {
    const route = useRoute()

    const useType = () => {
      const isCreate = computed(() => {
        return route.name === 'CreateColor'
      })

      const isEdit = computed(() => {
        return route.name === 'EditColor'
      })

      const isView = computed(() => {
        return route.name === 'ViewColor'
      })

      const isCopy = computed(() => {
        return route.name === 'CopyColor'
      })

      return {
        isCreate,
        isEdit,
        isView,
        isCopy
      }
    }

    const { isCreate, isEdit, isView, isCopy } = useType()

    const id = computed(() => {
      return route.query.id
    })

    const versionId = computed(() => {
      return route.query.versionId
    })

    const useConst = () => {
      // 颜色库枚举
      const colorLibraryMap = colorLibraryConst.colorLibraryMap

      const colorLibraryList = colorLibraryConst.colorLibraryList

      // 状态枚举
      const statusList = statusConst.statusList
      const statusMap = statusConst.statusMap

      // 色系下拉列表
      const colorCategoryList = ref<ColorCategoryListAPI.Data[]>([])
      const useFetchColorCategoryList = async () => {
        const [error, result] = await getColorCategoryList()
        if (error === null && result?.datas) {
          colorCategoryList.value = result?.datas
        }
      }
      const brandCategoryList = computed(() => {
        return colorCategoryList.value
          .filter((e) => e.value.BRAND)
          .map((e) => ({ ...e, value: e.value.BRAND }))
      })

      const pantoneCategoryList = computed(() => {
        return colorCategoryList.value
          .filter((e) => e.value.PANTONE)
          .map((e) => ({ ...e, value: e.value.PANTONE }))
      })

      return {
        colorLibraryMap,
        colorLibraryList,
        statusMap,
        statusList,
        brandCategoryList,
        pantoneCategoryList,
        useFetchColorCategoryList
      }
    }

    const {
      colorLibraryList,
      statusMap,
      statusList,
      brandCategoryList,
      pantoneCategoryList,
      useFetchColorCategoryList
    } = useConst()

    const activeNames = ref(['1', '2', '3'])

    const formRef = ref<FormInstance>()

    const brandFormData = {
      rgb: '',
      reference: [],
      thumbnail: undefined,
      remark: ''
    }

    const pantoneFormData = {
      code: '',
      standardShade: '',
      number: '',
      thumbnail: undefined
    }

    const commonFormData = {
      libraryCode: ColorLibraryEnum.BRAND,
      statusCode: '',
      id: undefined,
      needApprove: undefined,
      name: '',
      englishName: '',
      categoryCode: ''
    }

    const formData = ref<
      ColorInfoAPI.ColorInfo & {
        needApprove?: boolean
      }
    >({
      ...commonFormData,
      ...brandFormData
    })

    const handleLibraryChange = (value: string) => {
      const copyFormData = { ...formData.value }
      formData.value = {
        libraryCode: value,
        ...(value === ColorLibraryEnum.BRAND || value === ColorLibraryEnum.JF
          ? brandFormData
          : pantoneFormData)
      }
      Object.keys(commonFormData).forEach((key) => {
        formData.value[key] = copyFormData[key]
      })
      setTimeout(() => formRef.value?.clearValidate(), 0)
    }

    const formRules = ref<FormRules<Omit<typeof formData.value, 'productList'>>>({
      name: [
        {
          required: true,
          message: '请输入颜色名称',
          trigger: 'blur'
        }
      ],
      englishName: [
        {
          required: true,
          message: '请输入颜色英文名称',
          trigger: 'blur'
        }
      ],
      categoryCode: [
        {
          required: true,
          message: '请选择色系',
          trigger: 'change'
        }
      ],
      libraryCode: [
        {
          required: true,
          message: '请选择颜色库',
          trigger: 'change'
        }
      ],
      thumbnail: [
        {
          required: true,
          message: '请上传缩略图',
          trigger: 'input'
        }
      ],
      code: [
        {
          required: true,
          message: '请输入颜色编码',
          trigger: 'blur'
        }
      ],
      number: [
        {
          required: true,
          message: '请选择颜色',
          trigger: 'change'
        }
      ]
    })

    const isPantone = computed(() => {
      return formData.value.libraryCode === ColorLibraryEnum.PANTONE
    })

    const pantoneRGBImageRef = ref<HTMLElement>()
    const pantoneFormItem = () => (
      <>
        {commonFormItem()}
        <ElCol span={12}>
          <ElFormItem label="标准色号" prop="standardShade">
            <ElInput
              v-model={formData.value.standardShade}
              placeholder="请输入标准色号"
              maxlength="50"
              showWordLimit
            />
          </ElFormItem>
        </ElCol>
        <ElCol span={12}>
          <ElFormItem class="!mb-0" label="颜色选择器" prop="number">
            <ElColorPicker
              size="large"
              v-model={formData.value.number}
              onActiveChange={debounce((val) => (formData.value.number = val), 500)}
            />
          </ElFormItem>
          <ElFormItem label=" " prop="number">
            <RGBInput v-model={formData.value.number} v-model:rgb={formData.value.rgb} />
          </ElFormItem>
        </ElCol>
        <ElCol span={12}>
          <ElFormItem label="RGB缩略图" prop="thumbnail">
            <div
              ref={pantoneRGBImageRef}
              class={`w-60 h-60 bg-[${formData.value.number}]`}
              style={{ backgroundColor: formData.value.number }}
            />
          </ElFormItem>
        </ElCol>
      </>
    )

    const uploadRef = ref<InstanceType<typeof BaseUpload>>()
    const html2Blob = (dom: HTMLElement): Promise<Blob | false> => {
      return new Promise((resolve) => {
        dom2image
          .toBlob(dom, {
            width: 240,
            height: 240
          })
          .then((blob) => {
            if (blob) resolve(blob)
            else resolve(false)
          })
          .catch((error) => {
            console.error(error)
            resolve(false)
          })
      })
    }

    const useCombColors = () => {
      interface BasicColor {
        color?: ColorRGBListAPI.Data
        rate?: number
        area?: number
      }

      const basicColorData = ref<BasicColor[]>([])
      const basicColorRef = ref<VxeTableInstance>()

      const basicColorList = ref<ColorRGBListAPI.Data[]>([])
      const getBasicColorList = async () => {
        const [error, result] = await getColorRGBList()
        if (error === null && result?.datas) {
          basicColorList.value = result.datas
        }
        basicColorData.value = basicColorData.value.filter((e) =>
          basicColorList.value.find((color) => color.id === e.color?.id)
        )
        basicColorRef.value?.reloadData(basicColorData.value)
      }

      const handleCombChange = debounce(async () => {
        const val = basicColorData.value
        const sum = val.reduce((pre, cur) => pre + (cur.rate || 0), 0)
        if (sum !== 100) {
          return
        }
        const hasRGB = val.every((item) => item.color?.id)
        if (!hasRGB) {
          return
        }
        const hasRepeat = new Set(val.map((item) => item.color?.id)).size !== val.length
        if (hasRepeat) {
          ElMessage.warning('颜色不能重复')
          return
        }
        if (formData.value.thumbnail) {
          ElMessage.warning('请先删除缩略图')
          return
        }
        const dom = generateCombDom(val)
        const loading = ElLoading.service({
          fullscreen: true,
          lock: true,
          text: '生成缩略图中...',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        const blob = await html2Blob(dom)
        if (blob) {
          const elUploadInstance = uploadRef.value?.uploadRef
          const file = new File([blob], 'color.png', { type: 'image/png' })
          if (elUploadInstance) {
            elUploadInstance.clearFiles()
            elUploadInstance.handleStart(file as UploadRawFile)
            elUploadInstance.submit()
          }
        }
        loading.close()
      }, 500)
      const generateCombDom = (data: BasicColor[]) => {
        const container = document.getElementById('color-image')!
        while (container.firstChild) {
          container.removeChild(container.firstChild)
        }
        const containerArea = 240 * 240
        const domList: HTMLElement[] = []
        data.forEach((e, i, a) => {
          const area = (containerArea * e.rate!) / 100
          const realArea = area + (a[i - 1]?.area || 0)
          e.area = realArea
          const width = (realArea * 2) ** 0.5
          const rate = (width / 240) * 100
          const dom = document.createElement('div')
          dom.style.position = 'absolute'
          dom.style.top = dom.style.left = '0'
          dom.style.clipPath = dom.style.clipPath = `polygon(${rate}% 0, 0 0, 0 0, 0 ${rate}%)`
          dom.style.width = dom.style.height = `${width}px`
          dom.style.backgroundImage = `linear-gradient(rgb(${e.color?.rgb}), rgb(${e.color?.rgb}))`
          domList.push(dom)
        })
        for (let i = domList.length - 1; i >= 0; i--) {
          container.appendChild(domList[i])
        }
        return container
      }
      return {
        basicColorData,
        basicColorRef,
        basicColorList,
        getBasicColorList,
        handleCombChange
      }
    }

    const { basicColorData, basicColorRef, handleCombChange, basicColorList, getBasicColorList } =
      useCombColors()

    const rgb = ref('')
    const generateBrandImage = async (rgb: string) => {
      const container = document.getElementById('color-image')!
      while (container.firstChild) {
        container.removeChild(container.firstChild)
      }
      const dom = document.createElement('div')
      dom.style.width = dom.style.height = `100%`
      dom.style.backgroundColor = rgb
      container.appendChild(dom)
      const loading = ElLoading.service({
        fullscreen: true,
        lock: true,
        text: '生成缩略图中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const blob = await html2Blob(container)
      if (blob) {
        const file = new File([blob], 'color.png', { type: 'image/png' })
        const [error, result] = await uploadFiles([file])
        if (error === null && result?.datas) {
          formData.value.thumbnail = result?.datas[0]
        } else {
          ElMessage.error('上传缩略图失败')
        }
      }
      loading.close()
    }
    let first = true
    watchDebounced(
      () => rgb.value,
      (val) => {
        if (isView.value) return
        if (first && (isCopy.value || isEdit.value)) {
          first = false
          return
        }
        generateBrandImage(val)
      },
      {
        debounce: 500
      }
    )

    const handlePickColor = (val: string) => {
      rgb.value = val
    }

    const brandFormItem = () => (
      <>
        {commonFormItem()}
        <ElCol span={12}>
          <ElFormItem label="RGB" prop="rgb">
            <ElColorPicker size="large" v-model={rgb.value} onActiveChange={handlePickColor} />
            <RGBInput v-model={rgb.value} v-model:rgb={formData.value.rgb} />
          </ElFormItem>
        </ElCol>
        <ElCol span={12}>
          <ElFormItem label="参考图" prop="referenceAddress">
            <BaseUpload
              name="files"
              accept="image/*"
              v-model={formData.value.reference}
              action={UPLOAD_URL}
              limit={20}
              multiple
              sizeLimit={1024 * 1024 * 100}
              drag
            >
              {{
                tip: () => (
                  <div class="text-xs text-gray-500 mt-1">
                    只能上传图片文件，且每张图片不超过100MB
                  </div>
                )
              }}
            </BaseUpload>
          </ElFormItem>
        </ElCol>
        <ElCol span={12}>
          <ElFormItem label="颜色缩略图" prop="thumbnail">
            <BaseUpload
              ref={uploadRef}
              name="files"
              accept="image/*"
              modelValue={formData.value.thumbnail ? [formData.value.thumbnail] : undefined}
              action={UPLOAD_URL}
              limit={1}
              sizeLimit={1024 * 1024 * 10}
              drag
              onUpdate:modelValue={(val: UploadValue[]) => {
                formData.value.thumbnail = val[0]
              }}
            >
              {{
                tip: () => (
                  <div class="text-xs text-gray-500 mt-1">只能上传图片文件，且不超过10MB</div>
                )
              }}
            </BaseUpload>
            <div class="fixed left-[-1000px] top-[-1000px]">
              <div
                id="color-image"
                class={{
                  'overflow-hidden': true,
                  'w-60': true,
                  'h-60': true,
                  relative: true
                }}
              />
            </div>
          </ElFormItem>
        </ElCol>
        <ElCol span={12}>
          <ElFormItem label="备注" prop="remark">
            <ElInput
              class="w-48"
              v-model={formData.value.remark}
              maxlength="500"
              showWordLimit
              autosize={{ minRows: 4, maxRows: 6 }}
              type="textarea"
            />
          </ElFormItem>
        </ElCol>
      </>
    )

    const handleCategoryChange = (val: string) => {
      if (val !== COMB_COLOR && basicColorData.value.length) {
        basicColorData.value = []
      }
    }

    const formCouldEdit = computed(() => {
      return (
        isCreate.value ||
        isCopy.value ||
        formData.value.statusCode === StatusEnum.DRAFT ||
        (!formData.value.referenced && formData.value.statusCode === StatusEnum.START)
      )
    })

    const referencedWatcher = watch(formData, () => {
      if (!formCouldEdit.value) {
        ElMessage.warning('当前颜色信息已被材料或产品引用')
      }
    })

    onBeforeRouteLeave(referencedWatcher)

    const commonFormItem = () => (
      <>
        {(isCreate.value || isCopy.value) && !isPantone.value ? null : (
          <ElCol span={24}>
            <ElFormItem label="颜色编码" prop="code" class="w-1/2">
              <ElInput
                class="w-48"
                v-model={formData.value.code}
                maxlength="20"
                showWordLimit
                disabled={isPantone.value ? !formCouldEdit.value : isView.value || isEdit.value}
                placeholder="请输入颜色编码"
              />
            </ElFormItem>
          </ElCol>
        )}
        <ElCol span={12}>
          <ElFormItem label="颜色名称" prop="name">
            <ElInput
              class="w-48"
              v-model={formData.value.name}
              disabled={!formCouldEdit.value}
              maxlength="50"
              clearable
              showWordLimit
              placeholder="请输入颜色名称"
            />
          </ElFormItem>
        </ElCol>
        <ElCol span={12}>
          <ElFormItem label="色系" prop="categoryCode">
            <ElSelectV2
              v-model={formData.value.categoryCode}
              disabled={!formCouldEdit.value}
              placeholder="请选择色系"
              options={isPantone.value ? pantoneCategoryList.value : brandCategoryList.value}
              clearable
              onChange={handleCategoryChange}
            />
          </ElFormItem>
        </ElCol>
        <ElCol span={12}>
          <ElFormItem label="颜色英文名称" prop="englishName">
            <ElInput
              class="w-48"
              v-model={formData.value.englishName}
              disabled={!formCouldEdit.value}
              maxlength="100"
              clearable
              showWordLimit
              placeholder="请输入颜色英文名称"
            />
          </ElFormItem>
        </ElCol>
      </>
    )

    const computedSubmitParams = computed<typeof formData.value>(() => {
      return {
        ...formData.value,
        combineDetail: basicColorData.value.map((e) => {
          return {
            colorId: e.color?.id || 0,
            percentage: e.rate!,
            name: e.color?.name || '',
            rgb: e.color?.rgb || ''
          }
        })
      }
    })

    const submitFn = computed(() => {
      if (isCreate.value || isCopy.value) {
        return createColor
      }
      if (isEdit.value) {
        return editColor
      }
      return createColor
    })

    const submitLoading = ref(false)
    const handleCommonSubmit = async () => {
      const valid = await formRef.value?.validate()
      if (valid) {
        submitLoading.value = true
        if (isCreate.value || isCopy.value) {
          let isClose = false
          await ElMessageBox({
            title: '操作确认',
            message: '确认发起飞书审批',
            type: 'warning',
            confirmButtonText: '确认',
            cancelButtonText: '保存为草稿状态',
            showCancelButton: true,
            distinguishCancelAndClose: true,
            beforeClose: (action, _, done) => {
              if (action === 'confirm') {
                formData.value.needApprove = true
                done()
              } else if (action === 'cancel') {
                formData.value.needApprove = false
                done()
              } else {
                isClose = true
                done()
              }
            }
          }).catch(() => {})
          if (isClose) {
            submitLoading.value = false
            return
          }
        }
        if (isEdit.value) {
          formData.value.id = Number(id.value)
        }
        const [error, result] = await submitFn.value(computedSubmitParams.value)
        submitLoading.value = false
        if (error === null && result) {
          useClosePage('ColorLibrary')
          ElMessage.success(result.msg || '保存成功')
        } else {
          ElMessage.error(error?.message || '保存失败')
        }
      }
    }

    const router = useRouter()

    async function handleConfirm() {
      if (isView.value) {
        router.push({
          name: 'EditColor',
          query: {
            id: id.value
          }
        })
        return
      }
      await handleSubmit()
    }
    async function handleSubmit() {
      if (isPantone.value) {
        const blob = await html2Blob(pantoneRGBImageRef.value!)
        if (!blob) {
          ElMessage.error('生成缩略图失败')
          return
        }
        const file = new File([blob], 'pantone.png', { type: 'image/png' })
        const [error, result] = await uploadFiles([file])
        if (error === null && result?.datas) {
          formData.value.thumbnail = result?.datas[0]
          await handleCommonSubmit()
        }
      } else {
        if (formData.value.categoryCode === COMB_COLOR) {
          // 组合色可以不添加一个颜色，但是添加了则必须100%
          const hasColor = basicColorData.value.every((e) => e.color?.id)
          if (!hasColor) {
            ElMessage.warning('请先选择组合颜色')
            return
          }
          const sum = basicColorData.value.reduce((pre, cur) => pre + (cur.rate || 0), 0)
          if (sum !== 100 && basicColorData.value.length) {
            ElMessage.warning('组合颜色百分比总和应为100%')
            return
          }
        }
        await handleCommonSubmit()
      }
    }

    const useGetColorInfo = async () => {
      if ((isEdit.value || isView.value || isCopy.value) && (id.value || versionId.value)) {
        const loading = ElLoading.service({
          fullscreen: true,
          text: '加载中...',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        const [error, result] = await (id.value
          ? viewColor(+id.value)
          : viewColorByVersionId(+versionId.value!))
        loading.close()
        if (error === null && result?.datas) {
          formData.value = result.datas
          if (isCopy.value) {
            // 复制时，清空图片信息
            formData.value.thumbnail = undefined
            formData.value.reference = []
          }
          basicColorData.value =
            result.datas.combineDetail?.map((e) => {
              return {
                color: {
                  ...e,
                  id: e.colorId
                },
                rate: e.percentage
              }
            }) || []
          await Promise.all([fetchMaterialList(), getBasicColorList()])
        }
      } else {
        await getBasicColorList()
      }
    }

    const materialList = ref<MaterialByColorIdAPI.List>([])
    const fetchMaterialList = async () => {
      const [error, result] = await queryMaterialByColor({
        colorId: formData.value.id,
        current: 1,
        size: 1000
      })
      if (error === null && result?.datas) {
        materialList.value = result.datas.records || []
      }
    }
    // 修改颜色
    const handleEditColor = () => {
      const status = formData.value.statusCode
      if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
        ElMessage.warning('审批中/禁用数据不允许修改')
        return
      }
      router.push({
        name: 'EditColor',
        query: {
          id: formData.value.id
        }
      })
    }
    onActivated(() => {
      if (isView.value) {
        useGetColorInfo()
      }
    })

    Promise.all([useGetColorInfo(), useFetchColorCategoryList()])
    return () => (
      <>
        <ContentWrap class="info-wrapper">
          <ElCollapse v-model={activeNames.value}>
            <ElCollapseItem name="1">
              {{
                title: () => <div class="font-bold text-base">基础信息</div>,
                default: () => (
                  <ElForm
                    ref={formRef}
                    model={formData.value}
                    disabled={isView.value}
                    rules={formRules.value}
                    labelWidth="auto"
                    scroll-to-error
                    scrollIntoViewOptions={{ behavior: 'smooth' }}
                  >
                    <ElRow gutter={20}>
                      <ElCol span={isView.value || isEdit.value ? 12 : 24}>
                        <ElFormItem label="颜色库" prop="libraryCode">
                          <ElRadioGroup
                            v-model={formData.value.libraryCode}
                            disabled={isEdit.value}
                            onChange={handleLibraryChange}
                          >
                            {colorLibraryList.map((e) => (
                              <ElRadio label={e.label} value={e.value} />
                            ))}
                          </ElRadioGroup>
                        </ElFormItem>
                      </ElCol>
                      {isView.value || isEdit.value ? (
                        <ElCol span={12}>
                          <ElFormItem label="状态" prop="status">
                            <ElRadioGroup v-model={formData.value.statusCode} disabled>
                              {statusList.map((e) => (
                                <ElRadio label={e.label} value={e.value} />
                              ))}
                            </ElRadioGroup>
                          </ElFormItem>
                        </ElCol>
                      ) : null}
                      {isPantone.value ? pantoneFormItem() : brandFormItem()}
                    </ElRow>
                  </ElForm>
                )
              }}
            </ElCollapseItem>
            {!isPantone.value && formData.value.categoryCode === COMB_COLOR ? (
              <ElCollapseItem name="2" title="组合色信息">
                {{
                  title: () => <div class="font-bold text-base">组合色信息</div>,
                  default: () => (
                    <>
                      {isView.value ? null : (
                        <ElButton
                          class="my-2"
                          type="primary"
                          text
                          disabled={!formCouldEdit.value}
                          onClick={() =>
                            basicColorData.value.push({ color: {}, rate: 0 }) &&
                            basicColorRef.value?.loadData(basicColorData.value)
                          }
                        >
                          添加
                        </ElButton>
                      )}
                      <VxeTable ref={basicColorRef} align="center" border>
                        <VxeColumn type="seq" title="序号" />
                        <VxeColumn title="基础颜色">
                          {{
                            default: ({ row }) =>
                              isView.value ? (
                                <span>{row.color.name}</span>
                              ) : (
                                <ElSelect
                                  v-model={row.color}
                                  filterable
                                  onChange={handleCombChange}
                                  valueKey="id"
                                  disabled={!formCouldEdit.value}
                                >
                                  {basicColorList.value.map((e) => (
                                    <ElOption value={e} label={e.name}></ElOption>
                                  ))}
                                </ElSelect>
                              )
                          }}
                        </VxeColumn>
                        <VxeColumn title="RGB缩略图">
                          {{
                            default: ({ row }) => (
                              <div class="flex justify-center">
                                <div
                                  class={`w-16 h-6 bg-[rgb(${row.color.rgb})]`}
                                  style={`background-color: rgb(${row.color.rgb})`}
                                />
                              </div>
                            )
                          }}
                        </VxeColumn>
                        <VxeColumn title="占比(%)">
                          {{
                            default: ({ row }) =>
                              isView.value ? (
                                <span>{row.rate}</span>
                              ) : (
                                <ElInputNumber
                                  v-model={row.rate}
                                  disabled={!formCouldEdit.value}
                                  min={1}
                                  max={99}
                                  controlsPosition="right"
                                  onChange={handleCombChange}
                                />
                              )
                          }}
                        </VxeColumn>
                        <VxeColumn title="操作">
                          {{
                            default: ({ rowIndex }) =>
                              isView.value ? null : (
                                <ElButton
                                  type="danger"
                                  text
                                  disabled={!formCouldEdit.value}
                                  onClick={() => {
                                    basicColorData.value.splice(rowIndex, 1) &&
                                      basicColorRef.value?.loadData(basicColorData.value)
                                  }}
                                >
                                  删除
                                </ElButton>
                              )
                          }}
                        </VxeColumn>
                      </VxeTable>
                    </>
                  )
                }}
              </ElCollapseItem>
            ) : null}
            {isView.value ? (
              <ElCollapseItem name="3" title="使用情况">
                {{
                  title: () => <div class="font-bold text-base">使用情况</div>,
                  default: () => (
                    <>
                      <div class="my-2">关联的材料</div>
                      <VxeTable data={materialList.value} cellConfig={{ height: 80 }}>
                        <VxeColumn type="seq" width="60" title="序号" />
                        <VxeColumn field="materialCode" title="材料编码" />
                        <VxeColumn
                          title="缩略图"
                          field="thumbnailAddress"
                          width="80"
                          cell-render={{ name: 'Image' }}
                        />
                        <VxeColumn field="materialCnName" title="材料名称" />
                        <VxeColumn field="materialEnName" title="材料英文名称" />
                        <VxeColumn field="materialVendorName" title="材料供应商名称" />
                        <VxeColumn field="materialColor" title="材料颜色" />
                        <VxeColumn field="colorLocation" title="色卡库位" />
                        <VxeColumn title="状态">
                          {{
                            default: ({ row }) => <span>{statusMap[row.statusCode]}</span>
                          }}
                        </VxeColumn>
                      </VxeTable>
                      <div class="my-2">关联的产品</div>
                      <VxeTable data={formData.value.productList} rowConfig={{ height: 80 }}>
                        <VxeColumn type="seq" width="60" title="序号" />
                        <VxeColumn field="productNumber" title="产品编码" />
                        <VxeColumn
                          title="产品主图"
                          field="thumbnail"
                          width="80"
                          cellRender={{ name: 'Image' }}
                        />
                        <VxeColumn field="productCategoryItemName" title="产品分类" />
                        <VxeColumn field="brandItemName" title="品牌" />
                        <VxeColumn field="launchSeasonItemName" title="开发季节" />
                        <VxeColumn field="meetingResultItemName" title="选品会结果" />
                        <VxeColumn field="allProductColorIdListItemName" title="产品配色" />
                        <VxeColumn field="sizeRangeIdItemName" title="尺码段" />
                        <VxeColumn field="assignedFactoryItemName" title="供应商" />
                        <VxeColumn field="dataStatusItemName" title="状态" />
                      </VxeTable>
                    </>
                  )
                }}
              </ElCollapseItem>
            ) : null}
          </ElCollapse>
        </ContentWrap>
        <ContentWrap class="mt-2">
          <div class="text-center">
            <ElButton onClick={() => useClosePage('ColorLibrary')}>返回</ElButton>
            {isView.value ? null : (
              <ElButton loading={submitLoading.value} type="primary" onClick={handleSubmit}>
                确定
              </ElButton>
            )}
            {formData.value.statusCode === StatusEnum.APPROVING ||
            formData.value.statusCode === StatusEnum.BAN ||
            !isView.value ? null : (
              <ElButton v-hasPermi="['editColor']" type="primary" onClick={handleEditColor}>
                <Icon size="20" icon="ep:edit" />
                修改颜色
              </ElButton>
            )}
          </div>
        </ContentWrap>
      </>
    )
  }
})
</script>

<style scoped>
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}

:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>

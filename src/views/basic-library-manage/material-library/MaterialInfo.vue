<script lang="ts" setup>
import type { CascaderInstance, CascaderProps, FormInstance, FormRules } from 'element-plus'
import { ElLoading, ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import {
  createMaterial,
  CreateMaterialAPI,
  editMaterial,
  getVendorList,
  MaterialInfoAPI,
  ModelVendorSelectApi,
  viewMaterial,
  viewMaterialByVersion
} from '@/views/basic-library-manage/material-library/api/materialInfo'
import { UPLOAD_URL } from '@/views/basic-library-manage/api/upload'
import { MaterialCategoryEnum, statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import { Icon } from '@/components/Icon'
import ColorInfoDialog from '@/views/basic-library-manage/components/ColorInfoDialog.vue'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'
import BaseUpload from '@/components/Upload/BaseUpload.vue'
import { ColorListPageAPI } from '@/views/basic-library-manage/color-library/api/color-list'
import { DictValueAPI } from '@/views/basic-library-manage/api/common'
import { VxeTableInstance } from 'vxe-table'
import { useMaterial } from '@/views/basic-library-manage/sample-manage/components/hooks'
import { MaterialNameEnum } from '@/views/basic-library-manage/material-library/const'
import { ProductSkcInfoPageAPI } from '@/api/productSkcInfo/types'
import SkcInfoDialog from '@/views/basic-library-manage/sample-manage/components/SkcInfoDialog.vue'
import { MaterialListAPI } from '@/views/basic-library-manage/material-library/api/material-list'

defineOptions({
  name: 'MaterialInfo'
})

const route = useRoute()

const useType = () => {
  const isCreate = computed(() => {
    return route.name === 'CreateMaterial'
  })

  const isEdit = computed(() => {
    return route.name === 'EditMaterial'
  })

  const isView = computed(() => {
    return route.name === 'ViewMaterial'
  })

  const isCopy = computed(() => {
    return route.name === 'CopyMaterial'
  })

  return {
    isCreate,
    isEdit,
    isView,
    isCopy
  }
}

const { isCreate, isEdit, isView, isCopy } = useType()

const id = computed(() => {
  return route.query.id
})

const versionId = computed(() => {
  return route.query.versionId
})

const useConst = () => {
  // 状态枚举
  const statusList = statusConst.statusList

  // 材料分类级联
  const { materialCategoryList } = useMaterial()

  const filterMaterialCategoryList = computed(() => {
    const filter: string[] = [
      MaterialCategoryEnum.PU,
      MaterialCategoryEnum.LEATHER,
      MaterialCategoryEnum.TEXTILE,
      MaterialCategoryEnum.PLASTIC
    ]
    return materialCategoryList.value.filter((item) => {
      return filter.includes(item.selectorEnValue!)
    })
  })

  const cascaderProps: CascaderProps = {
    label: 'selectorValue',
    value: 'selectorKey',
    children: 'childList',
    expandTrigger: 'hover' as const,
    emitPath: false
  }

  const dictStore = useBasicLibraryDictStore()
  const store = computed(() => ({
    brandMap: dictStore.brandMap,
    brandList: dictStore.brandList,
    materialUnitList: dictStore.materialUnitList,
    commonSeasonMap: dictStore.commonDevSeasonMap
  }))

  return {
    statusList,
    store,
    materialCategoryList: filterMaterialCategoryList,
    cascaderProps
  }
}

const { store, statusList, materialCategoryList, cascaderProps } = useConst()

const activeNames = ref(['1', '2', '3'])
const supplierList = ref<ModelVendorSelectApi.Data[]>([])

type FormModel = Partial<CreateMaterialAPI.Params & MaterialInfoAPI.BaseMaterial>
const formRef = ref<FormInstance>()
const formData = ref<FormModel>({
  brandIdList: [],
  materialBreadth: '',
  materialEnName: '',
  materialUnit: '',
  materialVendorList: [{}],
  otherImageFileList: [],
  parentCategoryCode: '',
  remark: '',
  thumbnailFile: undefined,
  materialCategoryId: undefined,
  ingredientEnableFlag: true,
  textureEnableFlag: true,
  thicknessEnableFlag: true,
  breadthEnableFlag: true,
  grainEnableFlag: true,
  gramWeightEnableFlag: true
})

const formRules = ref<FormRules<Omit<FormModel, 'productRespList'>>>({
  thumbnailFile: [
    {
      required: true,
      message: '请上传材料缩略图',
      trigger: 'change'
    }
  ],
  materialCategoryId: [
    {
      required: true,
      message: '请选择材料分类',
      trigger: 'change'
    }
  ],
  brandIdList: [
    {
      required: true,
      message: '请选择适用品牌',
      trigger: 'change'
    }
  ],
  materialEnName: [
    {
      required: true,
      message: '请输入材料英文名称',
      trigger: 'blur'
    }
  ],
  materialCnName: [
    {
      required: true,
      message: '请输入材料名称',
      trigger: 'blur'
    }
  ],
  materialPart: [
    {
      required: true,
      message: '请选择部位',
      trigger: 'blur'
    }
  ],
  materialThickness: [
    {
      required: true,
      message: '请选择厚度',
      trigger: 'change'
    }
  ],
  materialBreadth: [
    {
      required: true,
      message: '请选择宽幅',
      trigger: 'change'
    }
  ],
  materialGrain: [
    {
      required: true,
      message: '请选择纹路',
      trigger: 'change'
    }
  ],
  materialGramWeight: [
    {
      required: true,
      message: '请选择克重',
      trigger: 'change'
    }
  ],
  materialUnit: [
    {
      required: true,
      message: '请选择单位',
      trigger: 'change'
    }
  ],
  colorId: [
    {
      required: true,
      message: '请选择颜色',
      trigger: 'change'
    }
  ],
  insoleMaterial: [
    {
      required: true,
      message: '请选择鞋垫材质',
      trigger: 'change'
    }
  ],
  cushionMaterial: [
    {
      required: true,
      message: '请选择垫心材质',
      trigger: 'change'
    }
  ],
  'materialVendorList.0.materialVendorName': [{ required: true, message: '请输入材料供应商名称' }],
  'materialVendorList.0.vendorMaterialMouldCode': [
    { required: true, message: '请输入供应商材料编号' }
  ],
  'materialVendorList.0.materialVendorIdentification': [
    { required: true, message: '请输入材料供应商标识' }
  ],
  'materialVendorList.0.colorLocation': [{ required: true, message: '请输入色卡库位' }]
})

const formCouldEdit = computed(() => {
  return (
    isCreate.value ||
    isCopy.value ||
    formData.value.status === StatusEnum.DRAFT ||
    (!formData.value.useFlag && formData.value.status === StatusEnum.START)
  )
})

// 材料分类
const materialCategoryRef = ref<CascaderInstance | null>(null)

const materialCategoryCode = ref('')
const materialCnName = ref('')

watchEffect(() => {
  if (!formData.value.materialCategoryId || !materialCategoryList.value.length) {
    return
  }
  setTimeout(() => {
    const checked = materialCategoryRef.value?.getCheckedNodes(false)
    if (checked) {
      const path = checked[0]?.pathNodes || []
      materialCategoryCode.value = path.map((item) => item.data?.selectorCode).join('')
      formData.value.materialEnName = checked[0]?.data?.selectorEnValue as string
      materialCnName.value = checked[0]?.pathLabels.at(0) || ''
      // formData.value.materialCnName = materialCnName.value
      formData.value.materialParentId = checked[0]?.pathValues[0] as number
    }
  })
})

// 供应商信息
const materialVendor = ref<DictValueAPI.Data>({
  dictCnName: '',
  dictEnName: '',
  dictValue: ''
})

watch(
  () => materialVendor.value,
  (val) => {
    if (val.dictValue && formData.value.materialVendorList?.[0]) {
      const { contactPerson, telephone, dictCnName, dictValue } =
        supplierList.value.find((item) => item.code === val.dictValue) || {}
      formData.value.materialVendorList[0].materialVendorName = dictCnName
      formData.value.materialVendorList[0].materialVendorIdentification = dictValue?.toString()
      formData.value.materialVendorList[0].contactPerson = contactPerson
      formData.value.materialVendorList[0].materialVendorPhone = telephone
    }
  }
)

watch(
  () => formData.value.materialVendorList,
  (val) => {
    if (val?.[0]?.materialVendorName) {
      materialVendor.value.dictCnName = val[0].materialVendorName
      materialVendor.value.dictValue = val[0].materialVendorIdentification || ''
    }
  }
)

const isPU = computed(() => materialCnName.value === MaterialNameEnum.PU)

/**
 * 是否皮料
 */
const isLeather = computed(() => materialCnName.value === MaterialNameEnum.LEATHER)

/**
 * 是否纺织物
 */
const isTextile = computed(() => materialCnName.value === MaterialNameEnum.TEXTILE)

/**
 * 是否塑胶
 */
const isPlastic = computed(() => materialCnName.value === MaterialNameEnum.PLASTIC)

const isShowMaterialGrain = computed(() => {
  return isPU.value || isLeather.value
})

const isShowMaterialThickness = computed(() => {
  return isPU.value || isLeather.value || isTextile.value || isPlastic.value
})

const isShowMaterialBreadth = computed(() => {
  return isPU.value || isTextile.value || isPlastic.value
})

const isShowMaterialGramWeight = computed(() => {
  return isTextile.value
})

const submitFn = computed(() => {
  if (isCreate.value || isCopy.value) {
    return createMaterial
  }
  if (isEdit.value) {
    return editMaterial
  }
  return createMaterial
})

const repeatData = ref<MaterialListAPI.List>([])
const repeatDialogVisible = ref(false)
const submitLoading = ref(false)

const router = useRouter()
async function handleConfirm() {
  if (isView.value) {
    router.push({
      name: 'EditMaterial',
      query: {
        id: id.value
      }
    })
    return
  }
  await handleSubmit()
}

async function handleSubmit() {
  // 表单校验
  const valid = await formRef.value?.validate()
  if (valid) {
    submitLoading.value = true
    if (isEdit.value) {
      formData.value.id = Number(id.value)
    }
    const [error, result] = await submitFn.value(formData.value)
    submitLoading.value = false
    if (error === null && result) {
      if (result.datas) {
        repeatData.value = result.datas
        repeatDialogVisible.value = true
        return
      }
      useClosePage('MaterialLibrary')
      ElMessage.success(result.msg || '保存成功')
    } else {
      ElMessage.error(error?.message || '保存失败')
    }
  }
}

const handleClose = () => {
  useClosePage('MaterialLibrary')
}

const useGetMaterialInfo = async () => {
  if ((isEdit.value || isView.value || isCopy.value) && (id.value || versionId.value)) {
    const loading = ElLoading.service({
      fullscreen: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    const [error, result] = await (id.value
      ? viewMaterial(+id.value)
      : viewMaterialByVersion(+versionId.value!))
    loading.close()
    if (error === null && result?.datas) {
      formData.value = result.datas
      formData.value.thumbnailFile = result.datas.thumbnailFile
      formData.value.otherImageFileList = result.datas.otherImageFileList || []
      colorShow.value = (formData.value.colorCnName || '') + (formData.value.colorEnName || '')
      if (isCopy.value) {
        // 复制时，清空图片信息、关联信息
        formData.value.thumbnailFile = undefined
        formData.value.otherImageFileList = []
        // 1.6
        // formData.value.productRespList = []
        // formData.value.productCodeList = []
      }
    }
  }
}

const usePickColor = () => {
  const colorShow = ref('')
  const colorVisible = ref(false)
  const handleOpenColorInfoDialog = () => {
    if (isView.value || isEdit.value || !formCouldEdit.value) return
    colorVisible.value = true
  }
  const handlePickColor = (row: ColorListPageAPI.Row) => {
    colorShow.value = row.englishName || ''
    formData.value.colorId = row.id
    formData.value.colorCode = row.code
    formData.value.colorCnName = row.name || ''
  }
  return {
    colorShow,
    colorVisible,
    handlePickColor,
    handleOpenColorInfoDialog
  }
}
const { colorShow, colorVisible, handlePickColor, handleOpenColorInfoDialog } = usePickColor()

const usePickProduct = () => {
  const tableRef = ref<VxeTableInstance | null>(null)
  const productInfoDialogVisible = ref(false)
  const handleOpenProductInfoDialog = () => {
    if (isView.value) return
    productInfoDialogVisible.value = true
  }
  const handlePickProduct = (productList: ProductSkcInfoPageAPI.List[]) => {
    if (!formData.value.productSkcCodeList) {
      formData.value.productSkcCodeList = []
    }
    if (!formData.value.productSkcRespList) {
      formData.value.productSkcRespList = []
    }
    formData.value.productSkcRespList = formData.value.productSkcRespList.concat(
      productList.filter((e) => !formData.value.productSkcCodeList?.includes(e.id!))
    )
    formData.value.productSkcCodeList = [
      ...new Set(formData.value.productSkcCodeList.concat(productList?.map((e) => e.id!)))
    ]
    tableRef.value?.loadData(formData.value.productSkcRespList || [])
  }

  const handleDeleteProduct = (index: number) => {
    formData.value.productSkcCodeList?.splice(index, 1)
    formData.value.productSkcRespList?.splice(index, 1)
    tableRef.value?.loadData(formData.value.productSkcRespList || [])
  }

  return {
    tableRef,
    productInfoDialogVisible,
    handleOpenProductInfoDialog,
    handleDeleteProduct,
    handlePickProduct
  }
}

const {
  tableRef,
  productInfoDialogVisible,
  handleDeleteProduct,
  handleOpenProductInfoDialog,
  handlePickProduct
} = usePickProduct()

// 获取供应商列表
const _getVendorList = async () => {
  const [error, result] = await getVendorList()
  if (error === null && result) {
    supplierList.value = result.datas?.map((item) => {
      return {
        ...item,
        dictValue: item.code,
        dictCnName: item.name
      }
    })
  }
}
const handleEditMaterial = () => {
  const status = formData.value.status
  if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
    ElMessage.warning('审批中/禁用数据不允许修改')
    return
  }
  router.push({
    name: 'EditMaterial',
    query: {
      id: formData.value.id
    }
  })
}
onActivated(() => {
  if (isView.value) {
    useGetMaterialInfo()
  }
})

Promise.all([useGetMaterialInfo(), _getVendorList()])
</script>

<template>
  <Dialog v-model="repeatDialogVisible" append-to-body title="材料已存在" top="5vh" width="1200">
    <VxeTable
      :cell-config="{ height: 80 }"
      :data="repeatData"
      :max-height="400"
      :show-header-overflow="false"
      align="center"
    >
      <VxeColumn fixed="left" type="checkbox" width="40" />
      <VxeColumn fixed="left" title="序号" type="seq" width="60" />
      <VxeColumn field="materialCode" fixed="left" show-overflow title="材料编码" width="80">
        <template #default="{ row }: { row: MaterialListAPI.Row }">
          <router-link :to="{ name: 'ViewMaterial', query: { id: row.id } }">
            <span class="p-0 max-w-full cursor-pointer text-blue-500">
              {{ row.materialCode }}
            </span>
          </router-link>
        </template>
      </VxeColumn>
      <VxeColumn
        :cell-render="{ name: 'Image' }"
        field="thumbnailFile"
        fixed="left"
        title="缩略图"
        width="100"
      />
      <VxeColumn field="materialCategoryMixName" fixed="left" title="材料分类" width="80" />
      <VxeColumn field="productSkcRespList.0.brandItemName" min-width="100" title="适用品牌" />
      <VxeColumn field="materialCnName" min-width="240" title="材料名称" />
      <VxeColumn field="materialEnName" min-width="100" title="材料英文名称" />
      <VxeColumn field="materialPartCnName" min-width="100" title="部位" />
      <VxeColumn field="materialBreadth" title="宽幅(cm)" width="80" />
      <VxeColumn field="materialThickness" title="厚度(mm)" width="80" />
      <VxeColumn field="materialGramWeight" title="克重" width="80" />
      <VxeColumn field="materialGrain" title="纹路" width="80" />
      <VxeColumn field="colorCnName" title="材料颜色" width="120" />
      <VxeColumn field="productSkcRespList.0.colorName" title="产品配色" width="130" />
      <VxeColumn
        field="materialVendorList.0.materialVendorName"
        title="材料供应商名称"
        width="120"
      />
      <VxeColumn
        field="materialVendorList.0.materialVendorIdentification"
        title="材料供应商标识"
        width="120"
      />
      <VxeColumn
        field="materialVendorList.0.materialVendorPhone"
        title="供应商联系方式"
        width="120"
      />
      <VxeColumn
        field="materialVendorList.0.vendorMaterialMouldCode"
        title="供应商材料编号"
        width="120"
      />
      <VxeColumn field="materialVendorList.0.colorLocation" title="色卡库位" width="120" />
      <VxeColumn field="statusStr" fixed="right" title="状态" width="80" />
      <VxeColumn field="modifyByName" title="操作人" width="120" />
    </VxeTable>
  </Dialog>
  <SkcInfoDialog v-model="productInfoDialogVisible" is-material @submit="handlePickProduct" />
  <ColorInfoDialog v-model="colorVisible" @submit="handlePickColor" />

  <ContentWrap class="info-wrapper">
    <ElCollapse v-model="activeNames">
      <ElForm
        ref="formRef"
        :disabled="isView"
        :model="formData"
        :rules="formRules"
        :scroll-into-view-options="{ behavior: 'smooth' }"
        class="mt-2"
        labelWidth="auto"
        scroll-to-error
      >
        <ElCollapseItem name="1">
          <template #title>
            <div class="font-bold text-base">基础信息</div>
          </template>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="材料缩略图" prop="thumbnailFile">
                <BaseUpload
                  :action="UPLOAD_URL"
                  :limit="1"
                  :model-value="formData.thumbnailFile ? [formData.thumbnailFile] : []"
                  :sizeLimit="1024 * 1024 * 10"
                  accept="image/*"
                  drag
                  name="files"
                  @update:model-value="(val) => (formData.thumbnailFile = val[0])"
                >
                  <template #tip>
                    <div class="text-xs text-gray-500 mt-1">
                      只能上传一张图片文件，且每张图片不超过10MB
                    </div>
                  </template>
                </BaseUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="材料图片" prop="otherImageFileList">
                <BaseUpload
                  v-model="formData.otherImageFileList"
                  :action="UPLOAD_URL"
                  :limit="20"
                  :sizeLimit="1024 * 1024 * 100"
                  accept="image/*"
                  drag
                  multiple
                  name="files"
                >
                  <template #tip>
                    <div class="text-xs text-gray-500 mt-1">
                      只能上传图片文件，且每张图片不超过100MB
                    </div>
                  </template>
                </BaseUpload>
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isView" :span="12">
              <ElFormItem label="材料编码" prop="materialCode">
                <ElInput v-model="formData.materialCode" class="w-48" disabled />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="材料分类" prop="materialCategoryId">
                <ElCascader
                  ref="materialCategoryRef"
                  v-model="formData.materialCategoryId"
                  :disabled="isEdit"
                  :options="materialCategoryList"
                  :props="cascaderProps"
                  clearable
                  placeholder="请选择材料分类"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="适用品牌" prop="brandIdList">
                <ElSelect
                  v-model="formData.brandIdList"
                  :disabled="isEdit && formData.status !== StatusEnum.DRAFT"
                  clearable
                  multiple
                  placeholder="请选择适用品牌"
                >
                  <ElOption
                    v-for="e in store.brandList"
                    :key="e.dictValue"
                    :label="e.dictCnName"
                    :value="e.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="材料分类编码">
                <ElInput v-model="materialCategoryCode" class="w-48" disabled />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="材料英文名称" prop="materialEnName">
                <ElInput
                  v-model="formData.materialEnName"
                  :disabled="!formCouldEdit || isEdit"
                  class="w-48"
                  clearable
                  maxlength="100"
                  placeholder="请输入材料英文名称"
                  showWordLimit
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isView || isEdit" :span="12">
              <ElFormItem label="材料名称" prop="materialCnName">
                <ElInput
                  v-model="formData.materialCnName"
                  class="w-48"
                  disabled
                  placeholder="根据规则，自动生成材料名称"
                  showWordLimit
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isShowMaterialThickness" :span="12">
              <ElFormItem label="厚度 (mm)" prop="materialThickness">
                <SelectPlus
                  v-model="formData.materialThickness"
                  :disabled="!formData.thicknessEnableFlag"
                  api-key="MATERIAL_THINKNESS"
                  cache
                  clearable
                  filterable
                  placeholder="请选择厚度 (mm)"
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isShowMaterialBreadth" :span="12">
              <ElFormItem label="宽幅 (CM)" prop="materialBreadth">
                <SelectPlus
                  v-model="formData.materialBreadth"
                  :disabled="!formData.breadthEnableFlag"
                  api-key="MATERIAL_BREADTH"
                  clearable
                  filterable
                  placeholder="请选择宽幅 (CM)"
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isShowMaterialGrain" :span="12">
              <ElFormItem label="纹路" prop="materialGrain">
                <SelectPlus
                  v-model="formData.materialGrain"
                  :disabled="!formData.grainEnableFlag"
                  api-key="MATERIAL_GRAIN"
                  clearable
                  filterable
                  placeholder="请选择纹路"
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isShowMaterialGramWeight" :span="12">
              <ElFormItem label="克重" prop="materialGramWeight">
                <SelectPlus
                  v-model="formData.materialGramWeight"
                  :disabled="!formData.gramWeightEnableFlag"
                  api-key="MATERIAL_GRAM_WEIGHT"
                  clearable
                  filterable
                  placeholder="请选择克重"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="单位" prop="materialUnit">
                <ElSelect v-model="formData.materialUnit" clearable placeholder="请选择单位">
                  <ElOption
                    v-for="e in store.materialUnitList"
                    :key="e.dictValue"
                    :label="e.dictCnName"
                    :value="e.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="材料颜色" prop="colorId">
                <ElInput
                  v-model="colorShow"
                  :disabled="formData.useFlag || isEdit"
                  class="w-48"
                  placeholder="请选择"
                  readonly
                >
                  <template #prefix>
                    <Icon
                      :size="26"
                      class="cursor-pointer"
                      color="#409EFF"
                      icon="mdi:search"
                      @click="handleOpenColorInfoDialog"
                    />
                  </template>
                </ElInput>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="部位" prop="materialPart">
                <SelectPlus
                  v-model="formData.materialPart"
                  :disabled="isEdit"
                  api-key="MATERIAL_PART"
                  bind-item
                  clearable
                  placeholder="请选择部位"
                  @update:item-value="(val) => (formData.materialPartCnName = val.dictCnName)"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="细类" prop="subclass">
                <ElInput
                  v-model="formData.subclass"
                  class="w-48"
                  clearable
                  maxlength="100"
                  placeholder="请输入"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isLeather" :span="12">
              <ElFormItem label="利用率%" prop="subclass">
                <ElInput v-model="formData.useRate" class="w-48" clearable />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isLeather" :span="12">
              <ElFormItem label="张幅（SF）" prop="increaseRate">
                <ElInput v-model="formData.increaseRate" class="w-48" clearable />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="备注" prop="remark">
                <ElInput
                  v-model="formData.remark"
                  :autosize="{ minRows: 4, maxRows: 6 }"
                  :resize="isView ? 'none' : undefined"
                  class="w-48"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isView || isEdit" :span="12">
              <ElFormItem label="状态" prop="status">
                <ElRadioGroup v-model="formData.status" disabled>
                  <ElRadio
                    v-for="e in statusList"
                    :key="e.value"
                    :label="e.label"
                    :value="e.value"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
        <ElCollapseItem name="2">
          <template #title>
            <div class="font-bold text-base">关联供应商信息</div>
          </template>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="材料供应商名称" prop="materialVendorList.0.materialVendorName">
                <ElSelect
                  v-model="materialVendor"
                  :disabled="formData.useFlag || (isEdit && formData.status !== StatusEnum.DRAFT)"
                  filterable
                  placeholder="请选择材料供应商名称"
                  value-key="dictValue"
                >
                  <ElOption
                    v-for="item in supplierList"
                    :key="item.dictValue"
                    :disabled="item.useStatus === ModelVendorSelectApi.Status.UNUSE"
                    :label="item.dictCnName"
                    :value="item"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="色卡库位" prop="materialVendorList.0.colorLocation">
                <ElInput
                  v-model="formData.materialVendorList![0].colorLocation"
                  class="w-48"
                  maxlength="20"
                  placeholder="请输入色卡库位"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="材料供应商标识"
                prop="materialVendorList.0.materialVendorIdentification"
              >
                <ElInput
                  v-model="formData.materialVendorList![0].materialVendorIdentification"
                  class="w-48"
                  disabled
                  placeholder="请输入供应商标识"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="供应商联系人" prop="materialVendorList.0.contactPerson">
                <ElInput
                  v-model="formData.materialVendorList![0].contactPerson"
                  class="w-48"
                  disabled
                  maxlength="100"
                  placeholder="请输入供应商联系人"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="供应商联系方式" prop="materialVendorList.0.materialVendorPhone">
                <ElInput
                  v-model="formData.materialVendorList![0].materialVendorPhone"
                  class="w-48"
                  disabled
                  maxlength="100"
                  placeholder="请输入供应商联系方式"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="材料价格（¥）" prop="materialPrice">
                <ElInputNumber
                  v-model="formData.materialVendorList![0].materialPrice"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :value-on-clear="null"
                  class="min-w-48 number-input"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="供应商材料编号/模号"
                prop="materialVendorList.0.vendorMaterialMouldCode"
              >
                <ElInput
                  v-model="formData.materialVendorList![0].vendorMaterialMouldCode"
                  class="w-48"
                  maxlength="100"
                  placeholder="请输入供应商材料编号/模号"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="交货周期(天)" prop="deliveryCycle">
                <ElInputNumber
                  v-model="formData.materialVendorList![0].deliveryCycle"
                  :controls="false"
                  :min="1"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48 number-input"
                  step-strictly
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="供应商材料名称" prop="materialVendorList.0.vendorMaterialName">
                <ElInput
                  v-model="formData.materialVendorList![0].vendorMaterialName"
                  class="w-48"
                  maxlength="100"
                  placeholder="请输入供应商材料名称"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
      </ElForm>
      <ElCollapseItem name="3">
        <template #title>
          <div class="font-bold text-base">关联产品信息</div>
        </template>
        <ElButton
          v-if="!isView"
          class="m-2"
          text
          type="primary"
          @click="handleOpenProductInfoDialog"
        >
          搜索产品
        </ElButton>
        <VxeTable
          ref="tableRef"
          :cell-config="{ height: 80 }"
          :data="formData.productSkcRespList"
          align="center"
          border
        >
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn field="productNumber" title="产品编码" />
          <VxeColumn field="productCategoryItemName" title="产品分类" />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.brandMap } }"
            field="brand"
            title="品牌"
          />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.commonSeasonMap } }"
            field="launchSeason"
            title="开发季节"
          />
          <VxeColumn field="skcCode" title="SKC编号" />
          <VxeColumn
            :cell-render="{ name: 'Image' }"
            field="colorProofResultImg"
            title="齐色样样品图"
            width="120"
          />
          <VxeColumn
            :cell-render="{ name: 'Image' }"
            field="erpSkcImgUrl"
            title="白底图"
            width="120"
          />
          <VxeColumn
            :cell-render="{ name: 'Image' }"
            field="confirmProofResultImg"
            title="确认样样品图"
            width="120"
          />
          <VxeColumn field="colorName" title="产品配色" />
          <VxeColumn field="wmsColorName" title="WMS色号名称" />
          <VxeColumn field="mainFabricItemName" title="主要面料" />
          <VxeColumn field="targetAudienceItemName" title="适用人群" />
          <VxeColumn v-if="!isView" :show-overflow="false" title="操作">
            <template #default="{ rowIndex }">
              <ElButton text type="primary" @click="handleDeleteProduct(rowIndex)"> 删除 </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </ElCollapseItem>
    </ElCollapse>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton v-if="!isView" :loading="submitLoading" type="primary" @click="handleSubmit">
        确定
      </ElButton>
      <ElButton
        v-if="
          formData.status !== StatusEnum.APPROVING && formData.status !== StatusEnum.BAN && isView
        "
        v-hasPermi="['editMaterial']"
        type="primary"
        @click="handleEditMaterial"
      >
        <Icon size="20" icon="ep:edit" />
        修改材料
      </ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}

.number-input {
  :deep(.el-input__wrapper) {
    padding-left: 12px;
  }

  :deep(.el-input__inner) {
    text-align: left;
  }
}

:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>

<style lang="less">
:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}

.drag-cell {
  & > .vxe-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.drag-header-cell > .vxe-cell {
  &,
  & > .vxe-cell--title {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.vxe-table--render-default.vaild-msg--single,
.vxe-body--row:last-child:first-child,
.vxe-cell--valid {
  top: 0;
}
</style>

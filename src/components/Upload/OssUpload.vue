<script lang="ts" setup>
import { computed, useAttrs } from 'vue'
import { ajaxUpload } from '@/components/Business/UpLoad/helper'
import { OssResponse } from '@/components/Business/UpLoad/types'
import type { UploadFile, UploadFiles, UploadProps } from 'element-plus'
import { UploadValue } from './types'
import BaseUpload from './BaseUpload.vue'

defineOptions({
  name: 'OssUpload'
})
const attrs = useAttrs()

interface Props {
  modelValue?: UploadValue[]
  tableImage?: boolean
  tableFile?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: (): UploadValue[] => []
})

const getBindValue = computed<Omit<Props, 'modelValue' | 'httpRequest' | 'class'>>(() => {
  const delArr: string[] = ['class', 'modelValue', 'httpRequest']
  const obj = { ...attrs, ...props }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
})

const handleSuccess: UploadProps['onSuccess'] = (
  res: ResponseData<OssResponse>,
  uploadFile: UploadFile,
  uploadFiles: UploadFiles
) => {
  if (res.isSuccess) {
    const list = props.modelValue ? [...props.modelValue] : []
    const {
      datas: { objectName, originFileName, downLoadUrl }
    } = res
    list.push({
      uid: uploadFile.uid,
      fileName: originFileName,
      fileUrl: objectName,
      signatureUrl: downLoadUrl
    })
    emit('success', list, uploadFile, uploadFiles)
    emit('update:modelValue', list)
  }
}

const handlePreview = (file: UploadFile) => {
  emit('preview', file)
}

const emit = defineEmits<{
  (e: 'update:modelValue', val: UploadValue[]): void
  (e: 'preview', file: UploadFile): void
  (
    e: 'success',
    res: BaseFileDTO[] | undefined,
    uploadFile: UploadFile,
    uploadFiles: UploadFiles
  ): void
}>()
</script>
<template>
  <BaseUpload
    :http-request="ajaxUpload"
    :modelValue="modelValue"
    :on-success="handleSuccess"
    class="w-full"
    v-bind="getBindValue"
    @preview="handlePreview"
    @update:model-value="emit('update:modelValue', $event)"
  >
    <template #trigger>
      <slot name="trigger"></slot>
    </template>
    <template #tip>
      <slot name="tip"></slot>
    </template>
    <template #file="{ file }">
      <slot :file="file" name="file"></slot>
    </template>
  </BaseUpload>
</template>

<script setup lang="ts">
import { LayoutForm } from '@/components/LayoutForm'
import { Icon } from '@/components/Icon'
import type { FormFieldConfig, AdvancedCondition } from '../src/types'

defineOptions({
  name: 'AdvancedSearchForm'
})

// 组件属性
const props = withDefaults(
  defineProps<{
    // 表单配置
    formConfig: FormFieldConfig[]
    // 表单数据
    modelValue: Record<string, any>
    // 高级搜索条件
    advancedConditions?: AdvancedCondition[]
    // 加载状态
    loading?: boolean
    // 是否显示高级搜索
    showAdvancedSearch?: boolean
  }>(),
  {
    formConfig: () => [],
    modelValue: () => ({}),
    advancedConditions: () => [],
    loading: false,
    showAdvancedSearch: true
  }
)

// 事件
const emit = defineEmits<{
  (e: 'update:modelValue', val: Record<string, any>): void
  (e: 'search', formData: Record<string, any>): void
  (e: 'reset'): void
  (e: 'advanced-search', conditions: AdvancedCondition[]): void
}>()

const layoutFormRef = ref()

// 表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 高级搜索面板可见性
const advancedSearchVisible = ref(false)

// 高级搜索条件
const searchConditions = ref<AdvancedCondition[]>([])

// 可用的搜索字段
const searchFields = computed(() => {
  return props.formConfig.map((item) => ({
    label: item.label,
    value: item.field
  }))
})

// 可用的操作符
const searchOperators = [
  { label: '等于', value: 'eq' },
  { label: '不等于', value: 'ne' },
  { label: '包含', value: 'like' },
  { label: '大于', value: 'gt' },
  { label: '小于', value: 'lt' },
  { label: '大于等于', value: 'ge' },
  { label: '小于等于', value: 'le' }
]

// 基本搜索项和高级搜索项分离
const basicFormFields = computed(() => {
  return props.formConfig.filter((item) => !item.isAdvanced)
})

// 初始化高级搜索条件
const initAdvancedConditions = () => {
  if (props.advancedConditions && props.advancedConditions.length > 0) {
    searchConditions.value = JSON.parse(JSON.stringify(props.advancedConditions))
  } else {
    // 默认添加一个空条件
    searchConditions.value = [
      { field: props.formConfig[0]?.field || '', operator: 'like', value: '' }
    ]
  }
}

// 添加搜索条件
const addCondition = () => {
  searchConditions.value.push({
    field: props.formConfig[0]?.field || '',
    operator: 'like',
    value: ''
  })
}

// 移除搜索条件
const removeCondition = (index: number) => {
  searchConditions.value.splice(index, 1)
  if (searchConditions.value.length === 0) {
    addCondition()
  }
}

// 切换高级搜索面板
const toggleAdvancedSearch = () => {
  advancedSearchVisible.value = !advancedSearchVisible.value
  if (advancedSearchVisible.value && searchConditions.value.length === 0) {
    initAdvancedConditions()
  }
}

// 执行高级搜索
const handleAdvancedSearch = () => {
  // 验证条件是否有效
  const validConditions = searchConditions.value.filter(
    (condition) =>
      condition.field &&
      condition.operator &&
      condition.value !== undefined &&
      condition.value !== ''
  )

  if (validConditions.length === 0) {
    // 如果没有有效条件，使用基本搜索
    handleSearch()
    return
  }

  emit('advanced-search', validConditions)
  advancedSearchVisible.value = false
}

// 重置高级搜索
const resetAdvancedSearch = () => {
  initAdvancedConditions()
}

// 执行基本搜索
const handleSearch = async () => {
  if (!layoutFormRef.value) return

  try {
    await layoutFormRef.value.validate()
    emit('search', formData.value)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  if (!layoutFormRef.value) return
  layoutFormRef.value.formRef.resetFields()
  emit('reset')
}

// 监听高级搜索条件变化
watch(
  () => props.advancedConditions,
  () => {
    if (props.advancedConditions && props.advancedConditions.length > 0) {
      searchConditions.value = JSON.parse(JSON.stringify(props.advancedConditions))
    }
  },
  { deep: true }
)

// 组件挂载时初始化
onMounted(() => {
  initAdvancedConditions()
})

// 暴露方法
defineExpose({
  layoutFormRef,
  validate: async () => {
    if (!layoutFormRef.value) return false
    return await layoutFormRef.value.validate()
  },
  resetFields: () => {
    layoutFormRef.value?.formRef.resetFields()
  }
})
</script>

<template>
  <div class="advanced-search-form">
    <!-- 基本搜索表单 -->
    <LayoutForm
      ref="layoutFormRef"
      :model="formData"
      :loading="loading"
      query-form
      :span="6"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template v-for="(field, index) in basicFormFields" :key="index">
        <ElFormItem :label="field.label" :prop="field.field">
          <!-- 根据组件类型渲染不同的表单控件 -->
          <component
            :is="field.component"
            v-model="formData[field.field]"
            v-bind="field.props || {}"
          />
        </ElFormItem>
      </template>
      <!-- 高级搜索按钮 -->
      <template #button v-if="showAdvancedSearch">
        <ElButton type="primary" @click="toggleAdvancedSearch"> 高级搜索 </ElButton>
      </template>
    </LayoutForm>
    <!-- 高级搜索抽屉 -->
    <ElDrawer
      v-model="advancedSearchVisible"
      title="高级搜索"
      size="500px"
      :with-header="true"
      :destroy-on-close="false"
      close-on-click-modal
    >
      <div class="drawer-content">
        <div class="conditions">
          <div v-for="(condition, index) in searchConditions" :key="index" class="condition-item">
            <ElSelect v-model="condition.field" placeholder="选择字段" class="field-select">
              <ElOption
                v-for="field in searchFields"
                :key="field.value"
                :label="field.label"
                :value="field.value"
              />
            </ElSelect>

            <ElSelect v-model="condition.operator" placeholder="选择操作符" class="operator-select">
              <ElOption
                v-for="operator in searchOperators"
                :key="operator.value"
                :label="operator.label"
                :value="operator.value"
              />
            </ElSelect>

            <ElInput v-model="condition.value" placeholder="输入值" class="value-input" />

            <div class="condition-actions">
              <ElButton
                type="danger"
                circle
                plain
                size="small"
                @click="removeCondition(index)"
                :disabled="searchConditions.length === 1"
              >
                <Icon icon="ep:delete" />
              </ElButton>
            </div>
          </div>
        </div>

        <div class="add-condition">
          <ElButton type="primary" @click="addCondition">
            <Icon icon="ep:plus" />
            添加条件
          </ElButton>
        </div>

        <ElDivider />

        <div class="drawer-footer">
          <ElButton @click="resetAdvancedSearch">重置</ElButton>
          <ElButton type="primary" @click="handleAdvancedSearch">查询</ElButton>
          <ElButton @click="toggleAdvancedSearch">取消</ElButton>
        </div>
      </div>
    </ElDrawer>
  </div>
</template>

<style scoped lang="less">
.advanced-search-form {
  position: relative;
  width: 100%;
}

.drawer-content {
  display: flex;
  height: 100%;
  padding: 16px;
  flex-direction: column;
}

.conditions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  overflow-y: auto;
}

.condition-item {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  margin-bottom: 16px;
}

.field-select {
  width: 150px;
}

.operator-select {
  width: 120px;
}

.value-input {
  flex: 1;
  min-width: 200px;
}

.add-condition {
  margin-top: 16px;
  margin-bottom: 16px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}
</style>

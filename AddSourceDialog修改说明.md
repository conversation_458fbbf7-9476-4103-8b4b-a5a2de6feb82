# AddSourceDialog 修改说明

## 修改概述

根据您的需求，我已经修改了 AddSourceDialog 组件，使其只返回第一层选中的 code 编码集合，而不是所有层级的数据。

## 主要修改内容

### 1. 修改了 `handleSubmit` 方法

#### 原有问题
- 代码逻辑混乱，有重复的代码块
- 没有正确处理层级关系
- 返回的数据格式不明确

#### 修改后的逻辑

```typescript
const handleSubmit = () => {
  let checked = []
  
  // 1. 获取选中的记录
  if (formData.source === sourceEnum.enum) {
    // 枚举类型使用单选
    const row = enumItemTableRef.value?.getRadioRecord()
    if (!row) {
      ElMessage.warning('请选择来源')
      return
    }
    checked.push(row)
  } else {
    // 分类类型使用多选
    const rows = enumItemTableRef.value?.getCheckboxRecords() || []
    if (rows.length === 0) {
      ElMessage.warning('请选择来源')
      return
    }
    checked = rows
  }

  // 2. 过滤出第一层的节点
  const firstLevelNodes = checked.filter(item => {
    if (formData.source === sourceEnum.enum) {
      return true // 枚举类型通常没有层级关系
    } else {
      // 判断是否为第一层节点
      const parentId = item.parentId || item.parent_id || item.pid
      return !parentId || parentId === 0 || parentId === '0' || parentId === ''
    }
  })

  // 3. 提取第一层节点的 code 编码集合
  const firstLevelCodes = firstLevelNodes.map(item => {
    if (formData.source === sourceEnum.enum) {
      return item.code
    } else {
      return item.categoryCode || item.code
    }
  }).filter(code => code)

  // 4. 返回结果
  emit('submit', {
    type: formData.source,
    codes: firstLevelCodes,        // 新增：code集合
    nodes: firstLevelNodes,        // 新增：完整节点信息
    value: firstLevelCodes[0] || '', // 兼容：第一个值
    label: firstLevelNodes[0]?.name || firstLevelNodes[0]?.categoryCnName || ''
  })
}
```

### 2. 第一层节点的判断逻辑

#### 判断标准
- **枚举类型**: 通常没有层级关系，所有选中的都认为是第一层
- **分类类型**: 通过 `parentId`、`parent_id` 或 `pid` 字段判断
  - `parentId` 为 `null`、`undefined`、`0`、`'0'` 或空字符串的节点认为是第一层

#### 支持的父级字段
```typescript
const parentId = item.parentId || item.parent_id || item.pid
```

### 3. 返回数据格式

#### 新的返回格式
```typescript
{
  type: string,           // 数据源类型 (enum/category/material)
  codes: string[],        // 第一层选中的code编码集合 ⭐ 新增
  nodes: object[],        // 第一层选中的完整节点信息 ⭐ 新增
  value: string,          // 兼容原有格式：第一个code值
  label: string           // 兼容原有格式：第一个节点的显示名称
}
```

#### 示例数据
```javascript
// 选中多个第一层分类的情况
{
  type: "CATEGORY",
  codes: ["CAT001", "CAT002", "CAT003"],
  nodes: [
    { id: 1, categoryCode: "CAT001", categoryCnName: "服装", parentId: null },
    { id: 2, categoryCode: "CAT002", categoryCnName: "鞋类", parentId: null },
    { id: 3, categoryCode: "CAT003", categoryCnName: "配饰", parentId: null }
  ],
  value: "CAT001",
  label: "服装"
}
```

### 4. 更新了 DefaultValueSetter 组件

#### 修改了 `handleSourceSelect` 方法
```typescript
const handleSourceSelect = (sourceData) => {
  // 处理新的数据格式，支持多个code的情况
  const displayLabel = sourceData.codes && sourceData.codes.length > 1 
    ? `${sourceData.label} 等${sourceData.codes.length}项`
    : sourceData.label || sourceData.value

  localOptions.value = {
    label: displayLabel,
    value: sourceData.value,
    codes: sourceData.codes || [sourceData.value],  // 新增
    nodes: sourceData.nodes || [],                  // 新增
    type: sourceData.type
  }
}
```

## 使用示例

### 基本用法
```vue
<template>
  <AddSourceDialog
    v-model="dialogVisible"
    @submit="handleSourceSelect"
  />
</template>

<script setup>
const dialogVisible = ref(false)

const handleSourceSelect = (sourceData) => {
  console.log('第一层选中的code集合:', sourceData.codes)
  console.log('第一层选中的节点:', sourceData.nodes)
  
  // 使用code集合进行后续处理
  sourceData.codes.forEach(code => {
    console.log('处理code:', code)
  })
}
</script>
```

### 获取特定信息
```javascript
const handleSourceSelect = (sourceData) => {
  // 获取第一层code集合
  const firstLevelCodes = sourceData.codes
  
  // 获取第一层节点的中文名称
  const firstLevelNames = sourceData.nodes.map(node => 
    node.categoryCnName || node.name
  )
  
  // 获取第一层节点的完整信息
  const firstLevelNodes = sourceData.nodes
  
  console.log('第一层编码:', firstLevelCodes)
  console.log('第一层名称:', firstLevelNames)
  console.log('第一层节点:', firstLevelNodes)
}
```

## 注意事项

1. **向后兼容**: 保留了原有的 `value` 和 `label` 字段，确保现有代码不会出错
2. **多选支持**: 新增的 `codes` 和 `nodes` 字段支持多选场景
3. **层级过滤**: 自动过滤掉非第一层的节点，只返回第一层的数据
4. **数据源适配**: 支持不同数据源的字段名称差异（如 `categoryCode` vs `code`）
5. **空值处理**: 自动过滤掉空的 code 值

## 调试信息

修改后的代码包含了详细的 console.log 输出，方便调试：
- 选中的原始数据
- 过滤后的第一层节点
- 提取的第一层code集合

您可以通过浏览器控制台查看这些调试信息，确认功能是否按预期工作。

## 测试建议

1. 测试枚举类型的选择
2. 测试产品分类的多选（包含多层级数据）
3. 测试材料分类的多选（包含多层级数据）
4. 验证只返回第一层节点的code集合
5. 确认向后兼容性
